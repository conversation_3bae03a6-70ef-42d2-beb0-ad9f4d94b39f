`timescale 1ns / 1ps

// TODO: myCPU测试bench
module myCPU_tb();

    // 输入信号
    reg         cpu_rst;
    reg         cpu_clk;
    reg  [31:0] inst;
    reg  [31:0] Bus_rdata;
    
    // 输出信号
    wire [13:0] inst_addr;
    wire [31:0] Bus_addr;
    wire        Bus_we;
    wire [31:0] Bus_wdata;
    
    // TODO: 实例化被测试模块
    myCPU uut (
        .cpu_rst    (cpu_rst),
        .cpu_clk    (cpu_clk),
        .inst_addr  (inst_addr),
        .inst       (inst),
        .Bus_addr   (Bus_addr),
        .Bus_rdata  (Bus_rdata),
        .Bus_we     (Bus_we),
        .Bus_wdata  (Bus_wdata)
    );
    
    // TODO: 时钟生成
    initial begin
        cpu_clk = 0;
        forever #5 cpu_clk = ~cpu_clk;  // 100MHz时钟
    end
    
    // TODO: 测试激励
    initial begin
        // 初始化
        cpu_rst = 1;
        inst = 32'h00000013;    // nop (addi x0, x0, 0)
        Bus_rdata = 32'h0;
        
        // 复位
        #20;
        cpu_rst = 0;
        
        // 测试指令序列
        #10;
        inst = 32'h00100093;    // addi x1, x0, 1
        
        #10;
        inst = 32'h00200113;    // addi x2, x0, 2
        
        #10;
        inst = 32'h002081b3;    // add x3, x1, x2
        
        #10;
        inst = 32'h00300213;    // addi x4, x0, 3
        
        #10;
        inst = 32'h004102b3;    // add x5, x2, x4
        
        // 继续运行一段时间
        #100;
        
        $display("测试完成");
        $finish;
    end
    
    // TODO: 监控信号变化
    always @(posedge cpu_clk) begin
        if (!cpu_rst) begin
            $display("时间: %0t, PC: %h, 指令: %h, ALU结果: %h", 
                     $time, {18'b0, inst_addr, 2'b0}, inst, Bus_addr);
        end
    end

endmodule
