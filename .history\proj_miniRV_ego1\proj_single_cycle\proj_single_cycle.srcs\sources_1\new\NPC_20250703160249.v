`timescale 1ns / 1ps

`include "defines.vh"

// TODO: NPC模块 - 下一个PC计算单元
// 功能：根据控制信号计算下一个PC值
module NPC (
    input  wire [31:0]  PC,         // 当前PC值
    input  wire [31:0]  IMM,        // 立即数
    input  wire [31:0]  ALU_C,      // ALU计算结果（用于jalr）
    input  wire [1:0]   npc_op,     // NPC操作选择
    output reg  [31:0]  npc         // 下一个PC值
);

    // TODO: 根据npc_op选择下一个PC值
    always @(*) begin
        case (npc_op)
            `NPC_PC4:     npc = PC + 4;         // 顺序执行：PC + 4
            `NPC_BRANCH:  npc = PC + IMM;       // 分支跳转：PC + imm
            `NPC_JUMP:    npc = ALU_C & ~1;     // jalr：ALU结果，最低位清零
            `NPC_JAL:     npc = PC + IMM;       // jal：PC + imm
            default:      npc = PC + 4;         // 默认顺序执行
        endcase
    end

endmodule
