`timescale 1ns / 1ps

// TODO: RF模块 - 寄存器堆
// 功能：32个32位寄存器，支持双读单写，x0恒为0
module RF (
    input  wire         clk,        // 时钟信号
    input  wire [4:0]   rR1,        // 读端口1地址（rs1）
    output wire [31:0]  rD1,        // 读端口1数据
    input  wire [4:0]   rR2,        // 读端口2地址（rs2）
    output wire [31:0]  rD2,        // 读端口2数据
    input  wire         we,         // 写使能
    input  wire [4:0]   wR,         // 写地址（rd）
    input  wire [31:0]  wD          // 写数据
);

    // TODO: 32个32位寄存器
    reg [31:0] registers [31:0];

    // TODO: 初始化寄存器（可选）
    integer i;
    initial begin
        for (i = 0; i < 32; i = i + 1) begin
            registers[i] = 32'h0;
        end
    end

    // TODO: 异步读操作
    assign rD1 = (rR1 == 5'b0) ? 32'h0 : registers[rR1];
    assign rD2 = (rR2 == 5'b0) ? 32'h0 : registers[rR2];

    // TODO: 同步写操作
    always @(posedge clk) begin
        if (we && wR != 5'b0) begin     // 写使能且不是x0寄存器
            registers[wR] <= wD;
        end
    end

endmodule