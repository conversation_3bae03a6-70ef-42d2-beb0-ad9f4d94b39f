#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Sat Jul  5 09:36:06 2025
# Process ID: 28012
# Current directory: H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1
# Command line: vivado.exe -log miniRV_SoC.vds -product Vivado -mode batch -messageDb vivado.pb -notrace -source miniRV_SoC.tcl
# Log file: H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/miniRV_SoC.vds
# Journal file: H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1\vivado.jou
#-----------------------------------------------------------
source miniRV_SoC.tcl -notrace
Command: synth_design -top miniRV_SoC -part xc7a35tcsg324-1
Starting synth_design
Attempting to get a license for feature 'Synthesis' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Synthesis' and/or device 'xc7a35t'
INFO: Launching helper process for spawning children vivado processes
INFO: Helper process launched with PID 4928 
---------------------------------------------------------------------------------
Starting RTL Elaboration : Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 469.098 ; gain = 101.152
---------------------------------------------------------------------------------
INFO: [Synth 8-6157] synthesizing module 'miniRV_SoC' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/miniRV_SoC.v:5]
INFO: [Synth 8-6157] synthesizing module 'cpuclk' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/.Xil/Vivado-28012-L/realtime/cpuclk_stub.v:5]
INFO: [Synth 8-6155] done synthesizing module 'cpuclk' (1#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/.Xil/Vivado-28012-L/realtime/cpuclk_stub.v:5]
INFO: [Synth 8-6157] synthesizing module 'myCPU' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/myCPU.v:5]
INFO: [Synth 8-226] default block is never used [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/myCPU.v:96]
INFO: [Synth 8-6157] synthesizing module 'PC' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/PC.v:4]
INFO: [Synth 8-6155] done synthesizing module 'PC' (2#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/PC.v:4]
INFO: [Synth 8-6157] synthesizing module 'NPC' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/NPC.v:5]
INFO: [Synth 8-6155] done synthesizing module 'NPC' (3#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/NPC.v:5]
INFO: [Synth 8-6157] synthesizing module 'RF' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/RF.v:3]
INFO: [Synth 8-6155] done synthesizing module 'RF' (4#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/RF.v:3]
INFO: [Synth 8-6157] synthesizing module 'ALU' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/ALU.v:5]
INFO: [Synth 8-6155] done synthesizing module 'ALU' (5#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/ALU.v:5]
INFO: [Synth 8-6157] synthesizing module 'SEXT' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/SEXT.v:5]
INFO: [Synth 8-6155] done synthesizing module 'SEXT' (6#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/SEXT.v:5]
INFO: [Synth 8-6157] synthesizing module 'Ctrl' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Ctrl.v:5]
INFO: [Synth 8-6155] done synthesizing module 'Ctrl' (7#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Ctrl.v:5]
INFO: [Synth 8-6155] done synthesizing module 'myCPU' (8#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/myCPU.v:5]
INFO: [Synth 8-6157] synthesizing module 'IROM' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/.Xil/Vivado-28012-L/realtime/IROM_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'IROM' (9#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/.Xil/Vivado-28012-L/realtime/IROM_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'Bridge' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Bridge.v:5]
INFO: [Synth 8-6155] done synthesizing module 'Bridge' (10#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Bridge.v:5]
INFO: [Synth 8-6157] synthesizing module 'DRAM' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/.Xil/Vivado-28012-L/realtime/DRAM_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'DRAM' (11#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/.Xil/Vivado-28012-L/realtime/DRAM_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'Dig' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Dig.v:5]
INFO: [Synth 8-226] default block is never used [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Dig.v:44]
INFO: [Synth 8-6155] done synthesizing module 'Dig' (12#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Dig.v:5]
INFO: [Synth 8-6157] synthesizing module 'Timer' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Timer.v:5]
INFO: [Synth 8-155] case statement is not full and has no default [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Timer.v:36]
INFO: [Synth 8-6155] done synthesizing module 'Timer' (13#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Timer.v:5]
INFO: [Synth 8-6157] synthesizing module 'Led' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Led.v:5]
INFO: [Synth 8-6155] done synthesizing module 'Led' (14#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Led.v:5]
INFO: [Synth 8-6157] synthesizing module 'Switch' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Switch.v:5]
INFO: [Synth 8-6155] done synthesizing module 'Switch' (15#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Switch.v:5]
INFO: [Synth 8-6157] synthesizing module 'Button' [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Button.v:5]
INFO: [Synth 8-6155] done synthesizing module 'Button' (16#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/Button.v:5]
INFO: [Synth 8-6155] done synthesizing module 'miniRV_SoC' (17#1) [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/miniRV_SoC.v:5]
WARNING: [Synth 8-3331] design Switch has unconnected port rst
WARNING: [Synth 8-3331] design Switch has unconnected port clk
WARNING: [Synth 8-3331] design Led has unconnected port wdata[31]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[30]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[29]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[28]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[27]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[26]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[25]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[24]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[23]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[22]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[21]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[20]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[19]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[18]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[17]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[16]
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[6]
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[4]
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[3]
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[2]
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[1]
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[0]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[6]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[5]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[4]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[3]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[2]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[1]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[0]
---------------------------------------------------------------------------------
Finished RTL Elaboration : Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 525.195 ; gain = 157.250
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 525.195 ; gain = 157.250
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 1 : Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 525.195 ; gain = 157.250
---------------------------------------------------------------------------------
INFO: [Device 21-403] Loading part xc7a35tcsg324-1
INFO: [Project 1-570] Preparing netlist for logic optimization

Processing XDC Constraints
Initializing timing engine
Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk/cpuclk_in_context.xdc] for cell 'Clkgen'
Finished Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk/cpuclk_in_context.xdc] for cell 'Clkgen'
Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM/IROM_in_context.xdc] for cell 'Mem_IROM'
Finished Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM/IROM_in_context.xdc] for cell 'Mem_IROM'
Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM/DRAM_in_context.xdc] for cell 'Mem_DRAM'
Finished Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM/DRAM_in_context.xdc] for cell 'Mem_DRAM'
Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc]
WARNING: [Constraints 18-619] A clock with name 'fpga_clk' already exists, overwriting the previous clock with the same name. [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc:2]
Finished Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc]
Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/constrs_1/new/miniRV_SoC.xdc]
Finished Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/constrs_1/new/miniRV_SoC.xdc]
INFO: [Project 1-236] Implementation specific constraints were found while reading constraint file [H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/constrs_1/new/miniRV_SoC.xdc]. These constraints will be ignored for synthesis but will be used in implementation. Impacted constraints are listed in the file [.Xil/miniRV_SoC_propImpl.xdc].
Resolution: To avoid this warning, move constraints listed in [.Xil/miniRV_SoC_propImpl.xdc] to another XDC file and exclude this new file from synthesis with the used_in_synthesis property (File Properties dialog in GUI) and re-run elaboration/synthesis.
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 866.453 ; gain = 0.000
Completed Processing XDC Constraints

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 866.453 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 866.453 ; gain = 0.000
Constraint Validation Runtime : Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 866.453 ; gain = 0.000
---------------------------------------------------------------------------------
Finished Constraint Validation : Time (s): cpu = 00:00:08 ; elapsed = 00:00:09 . Memory (MB): peak = 866.453 ; gain = 498.508
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Loading Part and Timing Information
---------------------------------------------------------------------------------
Loading part: xc7a35tcsg324-1
---------------------------------------------------------------------------------
Finished Loading Part and Timing Information : Time (s): cpu = 00:00:08 ; elapsed = 00:00:09 . Memory (MB): peak = 866.453 ; gain = 498.508
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Applying 'set_property' XDC Constraints
---------------------------------------------------------------------------------
Applied set_property IO_BUFFER_TYPE = NONE for fpga_clk. (constraint file  c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk/cpuclk_in_context.xdc, line 3).
Applied set_property CLOCK_BUFFER_TYPE = NONE for fpga_clk. (constraint file  c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk/cpuclk_in_context.xdc, line 4).
Applied set_property DONT_TOUCH = true for Clkgen. (constraint file  auto generated constraint, line ).
Applied set_property DONT_TOUCH = true for Mem_IROM. (constraint file  auto generated constraint, line ).
Applied set_property DONT_TOUCH = true for Mem_DRAM. (constraint file  auto generated constraint, line ).
---------------------------------------------------------------------------------
Finished applying 'set_property' XDC Constraints : Time (s): cpu = 00:00:08 ; elapsed = 00:00:09 . Memory (MB): peak = 866.453 ; gain = 498.508
---------------------------------------------------------------------------------
INFO: [Synth 8-5587] ROM size for "rf_we" is below threshold of ROM address width. It will be mapped to LUTs
INFO: [Synth 8-5587] ROM size for "rf_wsel" is below threshold of ROM address width. It will be mapped to LUTs
INFO: [Synth 8-5544] ROM "alu_op" won't be mapped to Block RAM because address size (3) smaller than threshold (5)
INFO: [Synth 8-5587] ROM size for "alub_sel" is below threshold of ROM address width. It will be mapped to LUTs
INFO: [Synth 8-5546] ROM "ram_we" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5545] ROM "access_sw" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "access_btn" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "access_dig" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "access_led" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "threshold" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 2 : Time (s): cpu = 00:00:08 ; elapsed = 00:00:09 . Memory (MB): peak = 866.453 ; gain = 498.508
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start RTL Component Statistics 
---------------------------------------------------------------------------------
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     32 Bit       Adders := 5     
	   3 Input     32 Bit       Adders := 1     
	   2 Input     20 Bit       Adders := 5     
	   2 Input      3 Bit       Adders := 1     
+---XORs : 
	   2 Input     32 Bit         XORs := 1     
+---Registers : 
	               32 Bit    Registers := 4     
	               20 Bit    Registers := 5     
	               16 Bit    Registers := 1     
	                5 Bit    Registers := 2     
	                3 Bit    Registers := 1     
	                1 Bit    Registers := 5     
+---RAMs : 
	             1024 Bit         RAMs := 1     
+---Muxes : 
	   4 Input     32 Bit        Muxes := 2     
	   2 Input     32 Bit        Muxes := 6     
	   5 Input     32 Bit        Muxes := 1     
	   3 Input     32 Bit        Muxes := 2     
	   2 Input     20 Bit        Muxes := 10    
	   3 Input      4 Bit        Muxes := 1     
	   9 Input      4 Bit        Muxes := 1     
	   8 Input      4 Bit        Muxes := 1     
	   7 Input      3 Bit        Muxes := 3     
	   9 Input      3 Bit        Muxes := 1     
	   9 Input      2 Bit        Muxes := 2     
	  13 Input      1 Bit        Muxes := 1     
	   3 Input      1 Bit        Muxes := 3     
	   2 Input      1 Bit        Muxes := 8     
	   9 Input      1 Bit        Muxes := 3     
---------------------------------------------------------------------------------
Finished RTL Component Statistics 
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start RTL Hierarchical Component Statistics 
---------------------------------------------------------------------------------
Hierarchical RTL Component report 
Module PC 
Detailed RTL Component Info : 
+---Registers : 
	               32 Bit    Registers := 1     
Module NPC 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     32 Bit       Adders := 2     
+---Muxes : 
	   4 Input     32 Bit        Muxes := 1     
Module RF 
Detailed RTL Component Info : 
+---RAMs : 
	             1024 Bit         RAMs := 1     
+---Muxes : 
	   2 Input     32 Bit        Muxes := 2     
Module ALU 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     32 Bit       Adders := 1     
	   3 Input     32 Bit       Adders := 1     
+---XORs : 
	   2 Input     32 Bit         XORs := 1     
+---Muxes : 
	  13 Input      1 Bit        Muxes := 1     
Module Ctrl 
Detailed RTL Component Info : 
+---Muxes : 
	   3 Input      4 Bit        Muxes := 1     
	   9 Input      4 Bit        Muxes := 1     
	   7 Input      3 Bit        Muxes := 3     
	   9 Input      3 Bit        Muxes := 1     
	   9 Input      2 Bit        Muxes := 2     
	   3 Input      1 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 1     
	   9 Input      1 Bit        Muxes := 3     
Module myCPU 
Detailed RTL Component Info : 
+---Muxes : 
	   2 Input     32 Bit        Muxes := 1     
	   4 Input     32 Bit        Muxes := 1     
Module Bridge 
Detailed RTL Component Info : 
+---Muxes : 
	   5 Input     32 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 4     
Module Dig 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input      3 Bit       Adders := 1     
+---Registers : 
	               32 Bit    Registers := 1     
	                3 Bit    Registers := 1     
+---Muxes : 
	   8 Input      4 Bit        Muxes := 1     
Module Timer 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     32 Bit       Adders := 2     
+---Registers : 
	               32 Bit    Registers := 2     
+---Muxes : 
	   2 Input     32 Bit        Muxes := 3     
	   3 Input     32 Bit        Muxes := 2     
	   2 Input      1 Bit        Muxes := 3     
	   3 Input      1 Bit        Muxes := 2     
Module Led 
Detailed RTL Component Info : 
+---Registers : 
	               16 Bit    Registers := 1     
Module Button 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     20 Bit       Adders := 5     
+---Registers : 
	               20 Bit    Registers := 5     
	                5 Bit    Registers := 2     
	                1 Bit    Registers := 5     
+---Muxes : 
	   2 Input     20 Bit        Muxes := 10    
---------------------------------------------------------------------------------
Finished RTL Hierarchical Component Statistics
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Part Resource Summary
---------------------------------------------------------------------------------
Part Resources:
DSPs: 90 (col length:60)
BRAMs: 100 (col length: RAMB18 60 RAMB36 30)
---------------------------------------------------------------------------------
Finished Part Resource Summary
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Cross Boundary and Area Optimization
---------------------------------------------------------------------------------
Warning: Parallel synthesis criteria is not met 
INFO: [Synth 8-5587] ROM size for "rf_we" is below threshold of ROM address width. It will be mapped to LUTs
INFO: [Synth 8-5587] ROM size for "rf_wsel" is below threshold of ROM address width. It will be mapped to LUTs
INFO: [Synth 8-5587] ROM size for "alub_sel" is below threshold of ROM address width. It will be mapped to LUTs
INFO: [Synth 8-5546] ROM "ram_we" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5545] ROM "Bridge/access_btn" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "Bridge/access_sw" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "Bridge/access_led" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "Bridge/access_dig" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
WARNING: [Synth 8-3917] design miniRV_SoC has port DN_DP0 driven by constant 0
WARNING: [Synth 8-3917] design miniRV_SoC has port DN_DP1 driven by constant 0
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[6]
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[4]
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[3]
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[2]
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[1]
WARNING: [Synth 8-3331] design Ctrl has unconnected port funct7[0]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[6]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[5]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[4]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[3]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[2]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[1]
WARNING: [Synth 8-3331] design SEXT has unconnected port inst[0]
WARNING: [Synth 8-3331] design NPC has unconnected port ALU_C[0]
---------------------------------------------------------------------------------
Finished Cross Boundary and Area Optimization : Time (s): cpu = 00:00:19 ; elapsed = 00:00:21 . Memory (MB): peak = 866.453 ; gain = 498.508
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Distributed RAM: Preliminary Mapping  Report (see note below)
+------------+-----------------------------+-----------+----------------------+---------------+
|Module Name | RTL Object                  | Inference | Size (Depth x Width) | Primitives    | 
+------------+-----------------------------+-----------+----------------------+---------------+
|miniRV_SoC  | Core_cpu/U_RF/registers_reg | Implied   | 32 x 32              | RAM32M x 12   | 
+------------+-----------------------------+-----------+----------------------+---------------+

Note: The table above is a preliminary report that shows the Distributed RAMs at the current stage of the synthesis flow. Some Distributed RAMs may be reimplemented as non Distributed RAM primitives later in the synthesis flow. Multiple instantiated RAMs are reported only once.
---------------------------------------------------------------------------------
Finished ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Applying XDC Timing Constraints
---------------------------------------------------------------------------------
INFO: [Synth 8-5578] Moved timing constraint from pin 'Clkgen/clk_out1' to pin 'Clkgen/bbstub_clk_out1/O'
WARNING: [Synth 8-565] redefining clock 'fpga_clk'
INFO: [Synth 8-5819] Moved 1 constraints on hierarchical pins to their respective driving/loading pins
---------------------------------------------------------------------------------
Finished Applying XDC Timing Constraints : Time (s): cpu = 00:00:26 ; elapsed = 00:00:29 . Memory (MB): peak = 866.453 ; gain = 498.508
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Timing Optimization
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Timing Optimization : Time (s): cpu = 00:00:48 ; elapsed = 00:00:50 . Memory (MB): peak = 1081.602 ; gain = 713.656
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Distributed RAM: Final Mapping  Report
+------------+-----------------------------+-----------+----------------------+---------------+
|Module Name | RTL Object                  | Inference | Size (Depth x Width) | Primitives    | 
+------------+-----------------------------+-----------+----------------------+---------------+
|miniRV_SoC  | Core_cpu/U_RF/registers_reg | Implied   | 32 x 32              | RAM32M x 12   | 
+------------+-----------------------------+-----------+----------------------+---------------+

---------------------------------------------------------------------------------
Finished ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Technology Mapping
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Technology Mapping : Time (s): cpu = 00:00:49 ; elapsed = 00:00:52 . Memory (MB): peak = 1081.602 ; gain = 713.656
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished IO Insertion : Time (s): cpu = 00:00:50 ; elapsed = 00:00:53 . Memory (MB): peak = 1081.602 ; gain = 713.656
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Renaming Generated Instances
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Instances : Time (s): cpu = 00:00:50 ; elapsed = 00:00:53 . Memory (MB): peak = 1081.602 ; gain = 713.656
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Rebuilding User Hierarchy
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Rebuilding User Hierarchy : Time (s): cpu = 00:00:50 ; elapsed = 00:00:53 . Memory (MB): peak = 1081.602 ; gain = 713.656
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Ports
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Ports : Time (s): cpu = 00:00:50 ; elapsed = 00:00:53 . Memory (MB): peak = 1081.602 ; gain = 713.656
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:50 ; elapsed = 00:00:53 . Memory (MB): peak = 1081.602 ; gain = 713.656
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Nets
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Nets : Time (s): cpu = 00:00:50 ; elapsed = 00:00:53 . Memory (MB): peak = 1081.602 ; gain = 713.656
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Writing Synthesis Report
---------------------------------------------------------------------------------

Report BlackBoxes: 
+------+--------------+----------+
|      |BlackBox name |Instances |
+------+--------------+----------+
|1     |cpuclk        |         1|
|2     |IROM          |         1|
|3     |DRAM          |         1|
+------+--------------+----------+

Report Cell Usage: 
+------+-------+------+
|      |Cell   |Count |
+------+-------+------+
|1     |DRAM   |     1|
|2     |IROM   |     1|
|3     |cpuclk |     1|
|4     |BUFG   |     1|
|5     |CARRY4 |   100|
|6     |LUT1   |    42|
|7     |LUT2   |   110|
|8     |LUT3   |   134|
|9     |LUT4   |   236|
|10    |LUT5   |   151|
|11    |LUT6   |   648|
|12    |MUXF7  |     4|
|13    |RAM32M |    12|
|14    |FDCE   |   298|
|15    |FDPE   |    12|
|16    |IBUF   |    22|
|17    |OBUF   |    40|
+------+-------+------+

Report Instance Areas: 
+------+-----------+-------+------+
|      |Instance   |Module |Cells |
+------+-----------+-------+------+
|1     |top        |       |  1876|
|2     |  Core_cpu |myCPU  |  1130|
|3     |    U_ALU  |ALU    |   140|
|4     |    U_NPC  |NPC    |    16|
|5     |    U_PC   |PC     |    76|
|6     |    U_RF   |RF     |   898|
|7     |  U_Button |Button |   267|
|8     |  U_Dig    |Dig    |   108|
|9     |  U_Led    |Led    |    16|
|10    |  U_Timer  |Timer  |   223|
+------+-----------+-------+------+
---------------------------------------------------------------------------------
Finished Writing Synthesis Report : Time (s): cpu = 00:00:50 ; elapsed = 00:00:53 . Memory (MB): peak = 1081.602 ; gain = 713.656
---------------------------------------------------------------------------------
Synthesis finished with 0 errors, 0 critical warnings and 17 warnings.
Synthesis Optimization Runtime : Time (s): cpu = 00:00:44 ; elapsed = 00:00:48 . Memory (MB): peak = 1081.602 ; gain = 372.398
Synthesis Optimization Complete : Time (s): cpu = 00:00:50 ; elapsed = 00:00:53 . Memory (MB): peak = 1081.602 ; gain = 713.656
INFO: [Project 1-571] Translating synthesized netlist
INFO: [Netlist 29-17] Analyzing 116 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1081.602 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 12 instances were transformed.
  RAM32M => RAM32M (RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMS32, RAMS32): 12 instances

INFO: [Common 17-83] Releasing license: Synthesis
69 Infos, 49 Warnings, 0 Critical Warnings and 0 Errors encountered.
synth_design completed successfully
synth_design: Time (s): cpu = 00:00:52 ; elapsed = 00:00:56 . Memory (MB): peak = 1081.602 ; gain = 725.125
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1081.602 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/miniRV_SoC.dcp' has been generated.
INFO: [runtcl-4] Executing : report_utilization -file miniRV_SoC_utilization_synth.rpt -pb miniRV_SoC_utilization_synth.pb
INFO: [Common 17-206] Exiting Vivado at Sat Jul  5 09:37:05 2025...
