#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Thu Jul  3 14:30:44 2025
# Process ID: 31424
# Current directory: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/cpuclk_synth_1
# Command line: vivado.exe -log cpuclk.vds -product Vivado -mode batch -messageDb vivado.pb -notrace -source cpuclk.tcl
# Log file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/cpuclk_synth_1/cpuclk.vds
# Journal file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/cpuclk_synth_1\vivado.jou
#-----------------------------------------------------------
source cpuclk.tcl -notrace
Command: synth_design -top cpuclk -part xc7a35tcsg324-1 -mode out_of_context
Starting synth_design
Attempting to get a license for feature 'Synthesis' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Synthesis' and/or device 'xc7a35t'
INFO: Launching helper process for spawning children vivado processes
INFO: Helper process launched with PID 6396 
---------------------------------------------------------------------------------
Starting RTL Elaboration : Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 466.914 ; gain = 99.453
---------------------------------------------------------------------------------
INFO: [Synth 8-6157] synthesizing module 'cpuclk' [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.v:70]
INFO: [Synth 8-6157] synthesizing module 'cpuclk_clk_wiz' [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_clk_wiz.v:68]
INFO: [Synth 8-6157] synthesizing module 'IBUF' [E:/Vivado/2018.3/scripts/rt/data/unisim_comp.v:20408]
	Parameter CAPACITANCE bound to: DONT_CARE - type: string 
	Parameter IBUF_DELAY_VALUE bound to: 0 - type: string 
	Parameter IBUF_LOW_PWR bound to: TRUE - type: string 
	Parameter IFD_DELAY_VALUE bound to: AUTO - type: string 
	Parameter IOSTANDARD bound to: DEFAULT - type: string 
INFO: [Synth 8-6155] done synthesizing module 'IBUF' (1#1) [E:/Vivado/2018.3/scripts/rt/data/unisim_comp.v:20408]
INFO: [Synth 8-6157] synthesizing module 'PLLE2_ADV' [E:/Vivado/2018.3/scripts/rt/data/unisim_comp.v:41045]
	Parameter BANDWIDTH bound to: OPTIMIZED - type: string 
	Parameter CLKFBOUT_MULT bound to: 33 - type: integer 
	Parameter CLKFBOUT_PHASE bound to: 0.000000 - type: float 
	Parameter CLKIN1_PERIOD bound to: 10.000000 - type: float 
	Parameter CLKIN2_PERIOD bound to: 0.000000 - type: float 
	Parameter CLKOUT0_DIVIDE bound to: 33 - type: integer 
	Parameter CLKOUT0_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT0_PHASE bound to: 0.000000 - type: float 
	Parameter CLKOUT1_DIVIDE bound to: 1 - type: integer 
	Parameter CLKOUT1_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT1_PHASE bound to: 0.000000 - type: float 
	Parameter CLKOUT2_DIVIDE bound to: 1 - type: integer 
	Parameter CLKOUT2_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT2_PHASE bound to: 0.000000 - type: float 
	Parameter CLKOUT3_DIVIDE bound to: 1 - type: integer 
	Parameter CLKOUT3_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT3_PHASE bound to: 0.000000 - type: float 
	Parameter CLKOUT4_DIVIDE bound to: 1 - type: integer 
	Parameter CLKOUT4_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT4_PHASE bound to: 0.000000 - type: float 
	Parameter CLKOUT5_DIVIDE bound to: 1 - type: integer 
	Parameter CLKOUT5_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT5_PHASE bound to: 0.000000 - type: float 
	Parameter COMPENSATION bound to: ZHOLD - type: string 
	Parameter DIVCLK_DIVIDE bound to: 4 - type: integer 
	Parameter IS_CLKINSEL_INVERTED bound to: 1'b0 
	Parameter IS_PWRDWN_INVERTED bound to: 1'b0 
	Parameter IS_RST_INVERTED bound to: 1'b0 
	Parameter REF_JITTER1 bound to: 0.010000 - type: float 
	Parameter REF_JITTER2 bound to: 0.010000 - type: float 
	Parameter STARTUP_WAIT bound to: FALSE - type: string 
INFO: [Synth 8-6155] done synthesizing module 'PLLE2_ADV' (2#1) [E:/Vivado/2018.3/scripts/rt/data/unisim_comp.v:41045]
INFO: [Synth 8-6157] synthesizing module 'BUFG' [E:/Vivado/2018.3/scripts/rt/data/unisim_comp.v:609]
INFO: [Synth 8-6155] done synthesizing module 'BUFG' (3#1) [E:/Vivado/2018.3/scripts/rt/data/unisim_comp.v:609]
INFO: [Synth 8-6155] done synthesizing module 'cpuclk_clk_wiz' (4#1) [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_clk_wiz.v:68]
INFO: [Synth 8-6155] done synthesizing module 'cpuclk' (5#1) [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.v:70]
---------------------------------------------------------------------------------
Finished RTL Elaboration : Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 523.051 ; gain = 155.590
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 523.051 ; gain = 155.590
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 1 : Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 523.051 ; gain = 155.590
---------------------------------------------------------------------------------
INFO: [Netlist 29-17] Analyzing 1 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Device 21-403] Loading part xc7a35tcsg324-1
INFO: [Project 1-570] Preparing netlist for logic optimization

Processing XDC Constraints
Initializing timing engine
Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_ooc.xdc] for cell 'inst'
Finished Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_ooc.xdc] for cell 'inst'
Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_board.xdc] for cell 'inst'
Finished Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_board.xdc] for cell 'inst'
Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc] for cell 'inst'
Finished Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc] for cell 'inst'
INFO: [Project 1-236] Implementation specific constraints were found while reading constraint file [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc]. These constraints will be ignored for synthesis but will be used in implementation. Impacted constraints are listed in the file [.Xil/cpuclk_propImpl.xdc].
Resolution: To avoid this warning, move constraints listed in [.Xil/cpuclk_propImpl.xdc] to another XDC file and exclude this new file from synthesis with the used_in_synthesis property (File Properties dialog in GUI) and re-run elaboration/synthesis.
INFO: [Timing 38-2] Deriving generated clocks
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 763.848 ; gain = 0.000
Parsing XDC File [C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/cpuclk_synth_1/dont_touch.xdc]
Finished Parsing XDC File [C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/cpuclk_synth_1/dont_touch.xdc]
Completed Processing XDC Constraints

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 763.906 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 763.906 ; gain = 0.000
Constraint Validation Runtime : Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.007 . Memory (MB): peak = 763.906 ; gain = 0.000
---------------------------------------------------------------------------------
Finished Constraint Validation : Time (s): cpu = 00:00:05 ; elapsed = 00:00:06 . Memory (MB): peak = 763.906 ; gain = 396.445
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Loading Part and Timing Information
---------------------------------------------------------------------------------
Loading part: xc7a35tcsg324-1
---------------------------------------------------------------------------------
Finished Loading Part and Timing Information : Time (s): cpu = 00:00:05 ; elapsed = 00:00:06 . Memory (MB): peak = 763.906 ; gain = 396.445
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Applying 'set_property' XDC Constraints
---------------------------------------------------------------------------------
Applied set_property DONT_TOUCH = true for inst. (constraint file  C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/cpuclk_synth_1/dont_touch.xdc, line 9).
---------------------------------------------------------------------------------
Finished applying 'set_property' XDC Constraints : Time (s): cpu = 00:00:05 ; elapsed = 00:00:06 . Memory (MB): peak = 763.906 ; gain = 396.445
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 2 : Time (s): cpu = 00:00:05 ; elapsed = 00:00:06 . Memory (MB): peak = 763.906 ; gain = 396.445
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start RTL Component Statistics 
---------------------------------------------------------------------------------
Detailed RTL Component Info : 
---------------------------------------------------------------------------------
Finished RTL Component Statistics 
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start RTL Hierarchical Component Statistics 
---------------------------------------------------------------------------------
Hierarchical RTL Component report 
---------------------------------------------------------------------------------
Finished RTL Hierarchical Component Statistics
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Part Resource Summary
---------------------------------------------------------------------------------
Part Resources:
DSPs: 90 (col length:60)
BRAMs: 100 (col length: RAMB18 60 RAMB36 30)
---------------------------------------------------------------------------------
Finished Part Resource Summary
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Cross Boundary and Area Optimization
---------------------------------------------------------------------------------
Warning: Parallel synthesis criteria is not met 
---------------------------------------------------------------------------------
Finished Cross Boundary and Area Optimization : Time (s): cpu = 00:00:05 ; elapsed = 00:00:06 . Memory (MB): peak = 763.906 ; gain = 396.445
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Applying XDC Timing Constraints
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Applying XDC Timing Constraints : Time (s): cpu = 00:00:11 ; elapsed = 00:00:12 . Memory (MB): peak = 819.703 ; gain = 452.242
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Timing Optimization
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Timing Optimization : Time (s): cpu = 00:00:11 ; elapsed = 00:00:12 . Memory (MB): peak = 819.703 ; gain = 452.242
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Technology Mapping
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Technology Mapping : Time (s): cpu = 00:00:11 ; elapsed = 00:00:12 . Memory (MB): peak = 829.281 ; gain = 461.820
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished IO Insertion : Time (s): cpu = 00:00:12 ; elapsed = 00:00:13 . Memory (MB): peak = 829.281 ; gain = 461.820
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Renaming Generated Instances
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Instances : Time (s): cpu = 00:00:12 ; elapsed = 00:00:13 . Memory (MB): peak = 829.281 ; gain = 461.820
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Rebuilding User Hierarchy
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Rebuilding User Hierarchy : Time (s): cpu = 00:00:12 ; elapsed = 00:00:13 . Memory (MB): peak = 829.281 ; gain = 461.820
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Ports
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Ports : Time (s): cpu = 00:00:12 ; elapsed = 00:00:13 . Memory (MB): peak = 829.281 ; gain = 461.820
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:12 ; elapsed = 00:00:13 . Memory (MB): peak = 829.281 ; gain = 461.820
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Nets
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Nets : Time (s): cpu = 00:00:12 ; elapsed = 00:00:13 . Memory (MB): peak = 829.281 ; gain = 461.820
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Writing Synthesis Report
---------------------------------------------------------------------------------

Report BlackBoxes: 
+-+--------------+----------+
| |BlackBox name |Instances |
+-+--------------+----------+
+-+--------------+----------+

Report Cell Usage: 
+------+----------+------+
|      |Cell      |Count |
+------+----------+------+
|1     |BUFG      |     2|
|2     |PLLE2_ADV |     1|
|3     |IBUF      |     1|
+------+----------+------+

Report Instance Areas: 
+------+---------+---------------+------+
|      |Instance |Module         |Cells |
+------+---------+---------------+------+
|1     |top      |               |     4|
|2     |  inst   |cpuclk_clk_wiz |     4|
+------+---------+---------------+------+
---------------------------------------------------------------------------------
Finished Writing Synthesis Report : Time (s): cpu = 00:00:12 ; elapsed = 00:00:13 . Memory (MB): peak = 829.281 ; gain = 461.820
---------------------------------------------------------------------------------
Synthesis finished with 0 errors, 0 critical warnings and 0 warnings.
Synthesis Optimization Runtime : Time (s): cpu = 00:00:08 ; elapsed = 00:00:10 . Memory (MB): peak = 829.281 ; gain = 220.965
Synthesis Optimization Complete : Time (s): cpu = 00:00:12 ; elapsed = 00:00:13 . Memory (MB): peak = 829.281 ; gain = 461.820
INFO: [Project 1-571] Translating synthesized netlist
INFO: [Netlist 29-17] Analyzing 1 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 842.777 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

INFO: [Common 17-83] Releasing license: Synthesis
25 Infos, 0 Warnings, 0 Critical Warnings and 0 Errors encountered.
synth_design completed successfully
synth_design: Time (s): cpu = 00:00:13 ; elapsed = 00:00:15 . Memory (MB): peak = 842.777 ; gain = 486.789
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 842.777 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/cpuclk_synth_1/cpuclk.dcp' has been generated.
INFO: [Coretcl 2-1648] Added synthesis output to IP cache for IP cpuclk, cache-ID = 1b61cdc1dabd502c
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 847.188 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/cpuclk_synth_1/cpuclk.dcp' has been generated.
INFO: [runtcl-4] Executing : report_utilization -file cpuclk_utilization_synth.rpt -pb cpuclk_utilization_synth.pb
INFO: [Common 17-206] Exiting Vivado at Thu Jul  3 14:31:02 2025...
