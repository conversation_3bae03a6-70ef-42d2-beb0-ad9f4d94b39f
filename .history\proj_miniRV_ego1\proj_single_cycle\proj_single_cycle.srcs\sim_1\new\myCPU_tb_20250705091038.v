`timescale 1ns / 1ps

// 全面测试21条必做指令的testbench
module myCPU_tb();

    // 输入信号
    reg         cpu_rst;
    reg         cpu_clk;
    reg  [31:0] inst;
    reg  [31:0] Bus_rdata;

    // 输出信号
    wire [13:0] inst_addr;
    wire [31:0] Bus_addr;
    wire        Bus_we;
    wire [31:0] Bus_wdata;

    // 测试计数器
    integer test_count;
    integer error_count;

    // 实例化被测试模块
    myCPU uut (
        .cpu_rst    (cpu_rst),
        .cpu_clk    (cpu_clk),
        .inst_addr  (inst_addr),
        .inst       (inst),
        .Bus_addr   (Bus_addr),
        .Bus_rdata  (Bus_rdata),
        .Bus_we     (Bus_we),
        .Bus_wdata  (Bus_wdata)
    );

    // 时钟生成
    initial begin
        cpu_clk = 0;
        forever #5 cpu_clk = ~cpu_clk;  // 100MHz时钟
    end

    // 测试任务
    task test_instruction;
        input [31:0] instruction;
        input [255:0] inst_name;
        begin
            inst = instruction;
            #10;  // 等待一个时钟周期
            test_count = test_count + 1;
            $display("测试 %0d: %s - 指令: %h, PC: %h, ALU结果: %h",
                     test_count, inst_name, instruction, {18'b0, inst_addr, 2'b0}, Bus_addr);
        end
    endtask

    // 主测试序列
    initial begin
        // 初始化
        test_count = 0;
        error_count = 0;
        cpu_rst = 1;
        inst = 32'h00000013;    // nop
        Bus_rdata = 32'h0;

        $display("=== 开始CPU指令测试 ===");

        // 复位
        #20;
        cpu_rst = 0;
        #10;

        // 测试立即数指令
        test_instruction(32'h00100093, "addi x1, x0, 1");      // addi x1, x0, 1
        test_instruction(32'h00200113, "addi x2, x0, 2");      // addi x2, x0, 2
        test_instruction(32'hfff00193, "addi x3, x0, -1");     // addi x3, x0, -1

        // 测试算术指令
        test_instruction(32'h002081b3, "add x3, x1, x2");      // add x3, x1, x2 (1+2=3)
        test_instruction(32'h40208233, "sub x4, x1, x2");      // sub x4, x1, x2 (1-2=-1)

        // 测试逻辑指令
        test_instruction(32'h0020f2b3, "and x5, x1, x2");      // and x5, x1, x2
        test_instruction(32'h0020e333, "or x6, x1, x2");       // or x6, x1, x2
        test_instruction(32'h0020c3b3, "xor x7, x1, x2");      // xor x7, x1, x2

        // 测试立即数逻辑指令
        test_instruction(32'h00f0f413, "andi x8, x1, 15");     // andi x8, x1, 15
        test_instruction(32'h00f0e493, "ori x9, x1, 15");      // ori x9, x1, 15
        test_instruction(32'h00f0c513, "xori x10, x1, 15");    // xori x10, x1, 15

        // 测试移位指令
        test_instruction(32'h00209593, "slli x11, x1, 2");     // slli x11, x1, 2
        test_instruction(32'h0020d613, "srli x12, x1, 2");     // srli x12, x1, 2
        test_instruction(32'h4020d693, "srai x13, x1, 2");     // srai x13, x1, 2

        test_instruction(32'h002097b3, "sll x15, x1, x2");     // sll x15, x1, x2
        test_instruction(32'h0020d833, "srl x16, x1, x2");     // srl x16, x1, x2
        test_instruction(32'h4020d8b3, "sra x17, x1, x2");     // sra x17, x1, x2

        // 测试LUI指令
        test_instruction(32'h12345937, "lui x18, 0x12345");    // lui x18, 0x12345

        // 测试内存指令 (需要设置Bus_rdata)
        Bus_rdata = 32'hdeadbeef;
        test_instruction(32'h00012983, "lw x19, 0(x2)");       // lw x19, 0(x2)
        test_instruction(32'h01312023, "sw x19, 0(x2)");       // sw x19, 0(x2)
