`timescale 1ns / 1ps

`include "defines.vh"

// TODO: NPC模块 - 下一个PC计算单元 - 严格按照设计规范
// 功能：根据控制信号选择下一个PC值（不做算术运算）
module NPC (
    input  wire [31:0]  PC,         // 当前PC值
    input  wire [31:0]  IMM,        // 立即数
    input  wire [31:0]  ALU_C,      // ALU计算结果（用于jalr）
    input  wire [1:0]   npc_op,     // NPC操作选择
    output reg  [31:0]  npc,        // 下一个PC值
    output wire [31:0]  pc4         // PC + 4
);

    // TODO: PC + 4计算
    assign pc4 = PC + 4;

    // TODO: 根据npc_op选择下一个PC值 - 严格按照设计规范（不做算术运算）
    always @(*) begin
        case (npc_op)
            `NPC_PC4:     npc = PC + 4;                    // 顺序执行：PC + 4
            `NPC_PCIMM:   npc = PC + IMM;                  // 分支和jal：PC + imm
            `NPC_RD1IMM:  npc = ALU_C & ~1;               // jalr：选择ALU结果并对齐
            default:      npc = PC + 4;                    // 默认顺序执行
        endcase
    end

endmodule