`timescale 1ns / 1ps

`include "defines.vh"

module Switch (
    input  wire         rst,       
    input  wire         clk,        
    input  wire [31:0]  addr,      
    output reg  [31:0]  rdata,      

    input  wire [15:0]  sw    
);

    // 读拨码开关状态
    always @(*) begin
        if (addr == `PERI_ADDR_SW) begin
            rdata = {16'h0, sw};  // 高16位为0，低16位为开关状态
        end else begin
            rdata = 32'h0;
        end
    end

endmodule
