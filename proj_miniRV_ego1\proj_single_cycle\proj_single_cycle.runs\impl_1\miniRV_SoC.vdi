#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Sat Jul  5 08:52:59 2025
# Process ID: 3448
# Current directory: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1
# Command line: vivado.exe -log miniRV_SoC.vdi -applog -product Vivado -messageDb vivado.pb -mode batch -source miniRV_SoC.tcl -notrace
# Log file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC.vdi
# Journal file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1\vivado.jou
#-----------------------------------------------------------
source miniRV_SoC.tcl -notrace
Command: open_checkpoint miniRV_SoC_routed.dcp

Starting open_checkpoint Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.017 . Memory (MB): peak = 250.648 ; gain = 0.000
INFO: [Netlist 29-17] Analyzing 2549 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 1 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2018.3
INFO: [Device 21-403] Loading part xc7a35tcsg324-1
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Timing 38-478] Restoring timing data from binary archive.
INFO: [Timing 38-479] Binary timing data restore complete.
INFO: [Project 1-856] Restoring constraints from binary archive.
INFO: [Project 1-853] Binary constraint restore complete.
Reading XDEF placement.
Reading placer database...
Reading XDEF routing.
Read XDEF File: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.793 . Memory (MB): peak = 1277.598 ; gain = 16.254
Restored from archive | CPU: 0.000000 secs | Memory: 0.000000 MB |
Finished XDEF File Restore: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.794 . Memory (MB): peak = 1277.598 ; gain = 16.254
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1277.645 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 2060 instances were transformed.
  RAM256X1S => RAM256X1S (MUXF7, MUXF7, MUXF8, RAMS64E, RAMS64E, RAMS64E, RAMS64E): 2048 instances
  RAM32M => RAM32M (RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMS32, RAMS32): 12 instances

INFO: [Project 1-604] Checkpoint was created with Vivado v2018.3 (64-bit) build 2405991
open_checkpoint: Time (s): cpu = 00:00:11 ; elapsed = 00:00:12 . Memory (MB): peak = 1277.645 ; gain = 1026.996
Command: write_bitstream -force miniRV_SoC.bit
Attempting to get a license for feature 'Implementation' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a35t'
Running DRC as a precondition to command write_bitstream
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'.
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC CFGBVS-1] Missing CFGBVS and CONFIG_VOLTAGE Design Properties: Neither the CFGBVS nor CONFIG_VOLTAGE voltage property is set in the current_design.  Configuration bank voltage select (CFGBVS) must be set to VCCO or GND, and CONFIG_VOLTAGE must be set to the correct configuration voltage, in order to determine the I/O voltage support for the pins in bank 0.  It is suggested to specify these either using the 'Edit Device Properties' function in the GUI or directly in the XDC file using the following syntax:

 set_property CFGBVS value1 [current_design]
 #where value1 is either VCCO or GND

 set_property CONFIG_VOLTAGE value2 [current_design]
 #where value2 is the voltage provided to configuration bank 0

Refer to the device configuration user guide for more information.
INFO: [Vivado 12-3199] DRC finished with 0 Errors, 1 Warnings
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
INFO: [Designutils 20-2272] Running write_bitstream with 2 threads.
Loading data files...
Loading site data...
Loading route data...
Processing options...
Creating bitmap...
Creating bitstream...
Writing bitstream ./miniRV_SoC.bit...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Project 1-120] WebTalk data collection is mandatory when using a WebPACK part without a full Vivado license. To see the specific WebTalk data collected for your design, open the usage_statistics_webtalk.html or usage_statistics_webtalk.xml file in the implementation directory.
INFO: [Common 17-83] Releasing license: Implementation
22 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:00:14 ; elapsed = 00:00:12 . Memory (MB): peak = 1791.180 ; gain = 513.535
INFO: [Common 17-206] Exiting Vivado at Sat Jul  5 08:53:24 2025...
