`timescale 1ns / 1ps

`include "defines.vh"

module Dig (
    input  wire         rst,        
    input  wire         clk,        
    input  wire [31:0]  addr,       
    input  wire         we,         
    input  wire [31:0]  wdata,      

    // 数码管硬件接口
    output reg  [ 7:0]  dig_en,   
    output reg          DN_A0, DN_A1,  
    output reg          DN_B0, DN_B1, 
    output reg          DN_C0, DN_C1, 
    output reg          DN_D0, DN_D1,  
    output reg          DN_E0, DN_E1,  
    output reg          DN_F0, DN_F1, 
    output reg          DN_G0, DN_G1, 
    output reg          DN_DP0, DN_DP1
);

    // 数据寄存器，存储要显示的32位数据
    reg [31:0] display_data;

    // 扫描计数器，动态扫描数码管
    reg [2:0] scan_cnt;
    reg [15:0] scan_clk_cnt;

    // 当前扫描位的4位数据
    wire [3:0] current_digit = (scan_cnt == 3'd0) ? display_data[3:0]   :
                               (scan_cnt == 3'd1) ? display_data[7:4]   :
                               (scan_cnt == 3'd2) ? display_data[11:8]  :
                               (scan_cnt == 3'd3) ? display_data[15:12] :
                               (scan_cnt == 3'd4) ? display_data[19:16] :
                               (scan_cnt == 3'd5) ? display_data[23:20] :
                               (scan_cnt == 3'd6) ? display_data[27:24] :
                                                     display_data[31:28];

    // 7段译码器 - 将4位16进制数转换为7段显示码 (负逻辑，低电平点亮)
    reg [7:0] seg_code;
    always @(*) begin
        case (current_digit)
            4'h0: seg_code = 8'h03;  // 0
            4'h1: seg_code = 8'h9F;  // 1
            4'h2: seg_code = 8'h25;  // 2
            4'h3: seg_code = 8'h0D;  // 3
            4'h4: seg_code = 8'h99;  // 4
            4'h5: seg_code = 8'h49;  // 5
            4'h6: seg_code = 8'h41;  // 6
            4'h7: seg_code = 8'h1F;  // 7
            4'h8: seg_code = 8'h01;  // 8
            4'h9: seg_code = 8'h19;  // 9
            4'hA: seg_code = 8'h11;  // A
            4'hB: seg_code = 8'hC1;  // b
            4'hC: seg_code = 8'h63;  // C
            4'hD: seg_code = 8'h85;  // d
            4'hE: seg_code = 8'h61;  // E
            4'hF: seg_code = 8'h71;  // F
            default: seg_code = 8'hFF;  // 全灭
        endcase
    end

    // 写数据寄存器
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            display_data <= 32'h0;
        end else if (we && addr == `PERI_ADDR_DIG) begin
            display_data <= wdata;
        end
    end

    // 扫描时钟分频 (1KHz扫描频率)
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            scan_clk_cnt <= 16'h0;
            scan_cnt <= 3'h0;
        end else begin
            if (scan_clk_cnt >= 16'd25000) begin  // 25MHz/25000 = 1KHz
                scan_clk_cnt <= 16'h0;
                scan_cnt <= scan_cnt + 1'b1;
            end else begin
                scan_clk_cnt <= scan_clk_cnt + 1'b1;
            end
        end
    end

    // 位选信号生成 (高电平有效)
    always @(*) begin
        dig_en = 8'h00;
        dig_en[scan_cnt] = 1'b1;
    end

    // 段选信号输出 (负逻辑，低电平点亮)
    always @(*) begin
        {DN_DP0, DN_G0, DN_F0, DN_E0, DN_D0, DN_C0, DN_B0, DN_A0} = seg_code;
        {DN_DP1, DN_G1, DN_F1, DN_E1, DN_D1, DN_C1, DN_B1, DN_A1} = seg_code;
    end

endmodule
