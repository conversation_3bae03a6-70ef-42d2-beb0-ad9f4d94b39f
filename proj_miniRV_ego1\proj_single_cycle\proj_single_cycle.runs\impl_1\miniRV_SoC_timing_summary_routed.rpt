Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul  5 09:39:24 2025
| Host         : L running 64-bit major release  (build 9200)
| Command      : report_timing_summary -max_paths 10 -file miniRV_SoC_timing_summary_routed.rpt -pb miniRV_SoC_timing_summary_routed.pb -rpx miniRV_SoC_timing_summary_routed.rpx -warn_on_violation
| Design       : miniRV_SoC
| Device       : 7a35t-csg324
| Speed File   : -1  PRODUCTION 1.23 2018-06-13
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Timing Summary Report

------------------------------------------------------------------------------------------------
| Timer Settings
| --------------
------------------------------------------------------------------------------------------------

  Enable Multi Corner Analysis               :  Yes
  Enable Pessimism Removal                   :  Yes
  Pessimism Removal Resolution               :  Nearest Common Node
  Enable Input Delay Default Clock           :  No
  Enable Preset / Clear Arcs                 :  No
  Disable Flight Delays                      :  No
  Ignore I/O Paths                           :  No
  Timing Early Launch at Borrowing Latches   :  false

  Corner  Analyze    Analyze    
  Name    Max Paths  Min Paths  
  ------  ---------  ---------  
  Slow    Yes        Yes        
  Fast    Yes        Yes        



check_timing report

Table of Contents
-----------------
1. checking no_clock
2. checking constant_clock
3. checking pulse_width_clock
4. checking unconstrained_internal_endpoints
5. checking no_input_delay
6. checking no_output_delay
7. checking multiple_clock
8. checking generated_clocks
9. checking loops
10. checking partial_input_delay
11. checking partial_output_delay
12. checking latch_loops

1. checking no_clock
--------------------
 There are 0 register/latch pins with no clock.


2. checking constant_clock
--------------------------
 There are 0 register/latch pins with constant_clock.


3. checking pulse_width_clock
-----------------------------
 There are 0 register/latch pins which need pulse_width check


4. checking unconstrained_internal_endpoints
--------------------------------------------
 There are 0 pins that are not constrained for maximum delay.

 There are 0 pins that are not constrained for maximum delay due to constant clock.


5. checking no_input_delay
--------------------------
 There are 22 input ports with no input delay specified. (HIGH)

 There are 0 input ports with no input delay but user has a false path constraint.


6. checking no_output_delay
---------------------------
 There are 38 ports with no output delay specified. (HIGH)

 There are 0 ports with no output delay but user has a false path constraint

 There are 0 ports with no output delay but with a timing clock defined on it or propagating through it


7. checking multiple_clock
--------------------------
 There are 0 register/latch pins with multiple clocks.


8. checking generated_clocks
----------------------------
 There are 0 generated clocks that are not connected to a clock source.


9. checking loops
-----------------
 There are 0 combinational loops in the design.


10. checking partial_input_delay
--------------------------------
 There are 0 input ports with partial input delay specified.


11. checking partial_output_delay
---------------------------------
 There are 0 ports with partial output delay specified.


12. checking latch_loops
------------------------
 There are 0 combinational latch loops in the design through latch input



------------------------------------------------------------------------------------------------
| Design Timing Summary
| ---------------------
------------------------------------------------------------------------------------------------

    WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
    -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
     13.738        0.000                      0                82977        0.154        0.000                      0                82977        3.000        0.000                       0                  8605  


All user specified timing constraints are met.


------------------------------------------------------------------------------------------------
| Clock Summary
| -------------
------------------------------------------------------------------------------------------------

Clock              Waveform(ns)       Period(ns)      Frequency(MHz)
-----              ------------       ----------      --------------
fpga_clk           {0.000 5.000}      10.000          100.000         
  clk_out1_cpuclk  {0.000 20.000}     40.000          25.000          
  clkfbout_cpuclk  {0.000 20.000}     40.000          25.000          


------------------------------------------------------------------------------------------------
| Intra Clock Table
| -----------------
------------------------------------------------------------------------------------------------

Clock                  WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
-----                  -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
fpga_clk                                                                                                                                                             3.000        0.000                       0                     1  
  clk_out1_cpuclk       13.738        0.000                      0                82977        0.154        0.000                      0                82977       18.750        0.000                       0                  8601  
  clkfbout_cpuclk                                                                                                                                                   12.633        0.000                       0                     3  


------------------------------------------------------------------------------------------------
| Inter Clock Table
| -----------------
------------------------------------------------------------------------------------------------

From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  


------------------------------------------------------------------------------------------------
| Other Path Groups Table
| -----------------------
------------------------------------------------------------------------------------------------

Path Group    From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    ----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  


------------------------------------------------------------------------------------------------
| Timing Details
| --------------
------------------------------------------------------------------------------------------------


---------------------------------------------------------------------------------------------------
From Clock:  fpga_clk
  To Clock:  fpga_clk

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack        3.000ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         fpga_clk
Waveform(ns):       { 0.000 5.000 }
Period(ns):         10.000
Sources:            { fpga_clk }

Check Type        Corner  Lib Pin           Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     PLLE2_ADV/CLKIN1  n/a            1.249         10.000      8.751      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Max Period        n/a     PLLE2_ADV/CLKIN1  n/a            52.633        10.000      42.633     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Slow    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Fast    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Slow    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Fast    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1



---------------------------------------------------------------------------------------------------
From Clock:  clk_out1_cpuclk
  To Clock:  clk_out1_cpuclk

Setup :            0  Failing Endpoints,  Worst Slack       13.738ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.154ns,  Total Violation        0.000ns
PW    :            0  Failing Endpoints,  Worst Slack       18.750ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             13.738ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_A/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        25.576ns  (logic 4.754ns (18.588%)  route 20.822ns (81.412%))
  Logic Levels:           23  (CARRY4=4 LUT2=4 LUT3=3 LUT4=2 LUT5=4 LUT6=6)
  Clock Path Skew:        0.027ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.427ns = ( 40.427 - 40.000 ) 
    Source Clock Delay      (SCD):    0.237ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.571     0.237    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X49Y47         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y47         FDCE (Prop_fdce_C_Q)         0.456     0.693 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.915     2.608    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X62Y45         LUT6 (Prop_lut6_I1_O)        0.124     2.732 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3/O
                         net (fo=1, routed)           0.640     3.372    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3_n_0
    SLICE_X62Y45         LUT6 (Prop_lut6_I3_O)        0.124     3.496 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1/O
                         net (fo=1, routed)           0.452     3.948    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1_n_0
    SLICE_X61Y45         LUT5 (Prop_lut5_I2_O)        0.124     4.072 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0/O
                         net (fo=19, routed)          0.762     4.833    Core_cpu/U_PC/spo[6]
    SLICE_X59Y49         LUT5 (Prop_lut5_I3_O)        0.124     4.957 r  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.263     5.220    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X59Y49         LUT5 (Prop_lut5_I4_O)        0.124     5.344 r  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          1.099     6.443    Core_cpu/U_PC/sext_op[2]
    SLICE_X51Y49         LUT3 (Prop_lut3_I2_O)        0.124     6.567 f  Core_cpu/U_PC/npc0_carry__2_i_6/O
                         net (fo=37, routed)          0.891     7.458    Core_cpu/U_RF/pc[31]_i_14
    SLICE_X47Y55         LUT3 (Prop_lut3_I0_O)        0.119     7.577 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.994     8.571    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X47Y50         LUT6 (Prop_lut6_I1_O)        0.332     8.903 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.785     9.688    Core_cpu/U_RF/sext_ext[13]
    SLICE_X49Y61         LUT4 (Prop_lut4_I0_O)        0.124     9.812 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.945    10.757    Core_cpu/U_RF/alu_B[13]
    SLICE_X47Y62         LUT3 (Prop_lut3_I2_O)        0.124    10.881 r  Core_cpu/U_RF/i__carry__2_i_5__1/O
                         net (fo=2, routed)           0.000    10.881    Core_cpu/U_ALU/Mem_DRAM_i_121_1[1]
    SLICE_X47Y62         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    11.431 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.431    Core_cpu/U_ALU/C0_inferred__0/i__carry__2_n_0
    SLICE_X47Y63         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.545 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.545    Core_cpu/U_ALU/C0_inferred__0/i__carry__3_n_0
    SLICE_X47Y64         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.659 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__4/CO[3]
                         net (fo=1, routed)           0.000    11.659    Core_cpu/U_ALU/C0_inferred__0/i__carry__4_n_0
    SLICE_X47Y65         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.313    11.972 f  Core_cpu/U_ALU/C0_inferred__0/i__carry__5/O[3]
                         net (fo=1, routed)           0.487    12.459    Core_cpu/U_ALU/data1[27]
    SLICE_X44Y65         LUT4 (Prop_lut4_I0_O)        0.306    12.765 f  Core_cpu/U_ALU/pc[27]_i_7/O
                         net (fo=1, routed)           0.484    13.248    Core_cpu/U_RF/pc[27]_i_2_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I4_O)        0.124    13.372 f  Core_cpu/U_RF/pc[27]_i_4/O
                         net (fo=1, routed)           0.433    13.805    Core_cpu/U_RF/pc[27]_i_4_n_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I2_O)        0.124    13.929 f  Core_cpu/U_RF/pc[27]_i_2/O
                         net (fo=6, routed)           1.361    15.291    Core_cpu/U_RF/Bus_addr__0[27]
    SLICE_X44Y56         LUT5 (Prop_lut5_I0_O)        0.152    15.443 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.849    16.292    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I1_O)        0.360    16.652 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.452    17.104    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I0_O)        0.326    17.430 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.700    18.130    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X37Y53         LUT2 (Prop_lut2_I1_O)        0.124    18.254 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.364    19.617    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X15Y64         LUT2 (Prop_lut2_I0_O)        0.124    19.741 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9472_9727_0_0_i_2/O
                         net (fo=6, routed)           0.930    20.671    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9472_9727_0_0_i_2_n_0
    SLICE_X13Y75         LUT6 (Prop_lut6_I5_O)        0.124    20.795 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_0_0_i_1/O
                         net (fo=128, routed)         5.018    25.813    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/WE
    SLICE_X64Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_A/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.518    40.427    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/WCLK
    SLICE_X64Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_A/CLK
                         clock pessimism             -0.163    40.264    
                         clock uncertainty           -0.180    40.084    
    SLICE_X64Y8          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.551    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_A
  -------------------------------------------------------------------
                         required time                         39.551    
                         arrival time                         -25.813    
  -------------------------------------------------------------------
                         slack                                 13.738    

Slack (MET) :             13.738ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_B/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        25.576ns  (logic 4.754ns (18.588%)  route 20.822ns (81.412%))
  Logic Levels:           23  (CARRY4=4 LUT2=4 LUT3=3 LUT4=2 LUT5=4 LUT6=6)
  Clock Path Skew:        0.027ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.427ns = ( 40.427 - 40.000 ) 
    Source Clock Delay      (SCD):    0.237ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.571     0.237    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X49Y47         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y47         FDCE (Prop_fdce_C_Q)         0.456     0.693 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.915     2.608    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X62Y45         LUT6 (Prop_lut6_I1_O)        0.124     2.732 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3/O
                         net (fo=1, routed)           0.640     3.372    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3_n_0
    SLICE_X62Y45         LUT6 (Prop_lut6_I3_O)        0.124     3.496 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1/O
                         net (fo=1, routed)           0.452     3.948    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1_n_0
    SLICE_X61Y45         LUT5 (Prop_lut5_I2_O)        0.124     4.072 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0/O
                         net (fo=19, routed)          0.762     4.833    Core_cpu/U_PC/spo[6]
    SLICE_X59Y49         LUT5 (Prop_lut5_I3_O)        0.124     4.957 r  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.263     5.220    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X59Y49         LUT5 (Prop_lut5_I4_O)        0.124     5.344 r  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          1.099     6.443    Core_cpu/U_PC/sext_op[2]
    SLICE_X51Y49         LUT3 (Prop_lut3_I2_O)        0.124     6.567 f  Core_cpu/U_PC/npc0_carry__2_i_6/O
                         net (fo=37, routed)          0.891     7.458    Core_cpu/U_RF/pc[31]_i_14
    SLICE_X47Y55         LUT3 (Prop_lut3_I0_O)        0.119     7.577 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.994     8.571    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X47Y50         LUT6 (Prop_lut6_I1_O)        0.332     8.903 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.785     9.688    Core_cpu/U_RF/sext_ext[13]
    SLICE_X49Y61         LUT4 (Prop_lut4_I0_O)        0.124     9.812 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.945    10.757    Core_cpu/U_RF/alu_B[13]
    SLICE_X47Y62         LUT3 (Prop_lut3_I2_O)        0.124    10.881 r  Core_cpu/U_RF/i__carry__2_i_5__1/O
                         net (fo=2, routed)           0.000    10.881    Core_cpu/U_ALU/Mem_DRAM_i_121_1[1]
    SLICE_X47Y62         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    11.431 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.431    Core_cpu/U_ALU/C0_inferred__0/i__carry__2_n_0
    SLICE_X47Y63         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.545 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.545    Core_cpu/U_ALU/C0_inferred__0/i__carry__3_n_0
    SLICE_X47Y64         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.659 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__4/CO[3]
                         net (fo=1, routed)           0.000    11.659    Core_cpu/U_ALU/C0_inferred__0/i__carry__4_n_0
    SLICE_X47Y65         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.313    11.972 f  Core_cpu/U_ALU/C0_inferred__0/i__carry__5/O[3]
                         net (fo=1, routed)           0.487    12.459    Core_cpu/U_ALU/data1[27]
    SLICE_X44Y65         LUT4 (Prop_lut4_I0_O)        0.306    12.765 f  Core_cpu/U_ALU/pc[27]_i_7/O
                         net (fo=1, routed)           0.484    13.248    Core_cpu/U_RF/pc[27]_i_2_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I4_O)        0.124    13.372 f  Core_cpu/U_RF/pc[27]_i_4/O
                         net (fo=1, routed)           0.433    13.805    Core_cpu/U_RF/pc[27]_i_4_n_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I2_O)        0.124    13.929 f  Core_cpu/U_RF/pc[27]_i_2/O
                         net (fo=6, routed)           1.361    15.291    Core_cpu/U_RF/Bus_addr__0[27]
    SLICE_X44Y56         LUT5 (Prop_lut5_I0_O)        0.152    15.443 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.849    16.292    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I1_O)        0.360    16.652 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.452    17.104    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I0_O)        0.326    17.430 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.700    18.130    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X37Y53         LUT2 (Prop_lut2_I1_O)        0.124    18.254 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.364    19.617    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X15Y64         LUT2 (Prop_lut2_I0_O)        0.124    19.741 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9472_9727_0_0_i_2/O
                         net (fo=6, routed)           0.930    20.671    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9472_9727_0_0_i_2_n_0
    SLICE_X13Y75         LUT6 (Prop_lut6_I5_O)        0.124    20.795 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_0_0_i_1/O
                         net (fo=128, routed)         5.018    25.813    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/WE
    SLICE_X64Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_B/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.518    40.427    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/WCLK
    SLICE_X64Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_B/CLK
                         clock pessimism             -0.163    40.264    
                         clock uncertainty           -0.180    40.084    
    SLICE_X64Y8          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.551    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_B
  -------------------------------------------------------------------
                         required time                         39.551    
                         arrival time                         -25.813    
  -------------------------------------------------------------------
                         slack                                 13.738    

Slack (MET) :             13.738ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_C/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        25.576ns  (logic 4.754ns (18.588%)  route 20.822ns (81.412%))
  Logic Levels:           23  (CARRY4=4 LUT2=4 LUT3=3 LUT4=2 LUT5=4 LUT6=6)
  Clock Path Skew:        0.027ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.427ns = ( 40.427 - 40.000 ) 
    Source Clock Delay      (SCD):    0.237ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.571     0.237    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X49Y47         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y47         FDCE (Prop_fdce_C_Q)         0.456     0.693 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.915     2.608    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X62Y45         LUT6 (Prop_lut6_I1_O)        0.124     2.732 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3/O
                         net (fo=1, routed)           0.640     3.372    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3_n_0
    SLICE_X62Y45         LUT6 (Prop_lut6_I3_O)        0.124     3.496 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1/O
                         net (fo=1, routed)           0.452     3.948    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1_n_0
    SLICE_X61Y45         LUT5 (Prop_lut5_I2_O)        0.124     4.072 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0/O
                         net (fo=19, routed)          0.762     4.833    Core_cpu/U_PC/spo[6]
    SLICE_X59Y49         LUT5 (Prop_lut5_I3_O)        0.124     4.957 r  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.263     5.220    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X59Y49         LUT5 (Prop_lut5_I4_O)        0.124     5.344 r  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          1.099     6.443    Core_cpu/U_PC/sext_op[2]
    SLICE_X51Y49         LUT3 (Prop_lut3_I2_O)        0.124     6.567 f  Core_cpu/U_PC/npc0_carry__2_i_6/O
                         net (fo=37, routed)          0.891     7.458    Core_cpu/U_RF/pc[31]_i_14
    SLICE_X47Y55         LUT3 (Prop_lut3_I0_O)        0.119     7.577 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.994     8.571    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X47Y50         LUT6 (Prop_lut6_I1_O)        0.332     8.903 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.785     9.688    Core_cpu/U_RF/sext_ext[13]
    SLICE_X49Y61         LUT4 (Prop_lut4_I0_O)        0.124     9.812 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.945    10.757    Core_cpu/U_RF/alu_B[13]
    SLICE_X47Y62         LUT3 (Prop_lut3_I2_O)        0.124    10.881 r  Core_cpu/U_RF/i__carry__2_i_5__1/O
                         net (fo=2, routed)           0.000    10.881    Core_cpu/U_ALU/Mem_DRAM_i_121_1[1]
    SLICE_X47Y62         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    11.431 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.431    Core_cpu/U_ALU/C0_inferred__0/i__carry__2_n_0
    SLICE_X47Y63         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.545 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.545    Core_cpu/U_ALU/C0_inferred__0/i__carry__3_n_0
    SLICE_X47Y64         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.659 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__4/CO[3]
                         net (fo=1, routed)           0.000    11.659    Core_cpu/U_ALU/C0_inferred__0/i__carry__4_n_0
    SLICE_X47Y65         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.313    11.972 f  Core_cpu/U_ALU/C0_inferred__0/i__carry__5/O[3]
                         net (fo=1, routed)           0.487    12.459    Core_cpu/U_ALU/data1[27]
    SLICE_X44Y65         LUT4 (Prop_lut4_I0_O)        0.306    12.765 f  Core_cpu/U_ALU/pc[27]_i_7/O
                         net (fo=1, routed)           0.484    13.248    Core_cpu/U_RF/pc[27]_i_2_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I4_O)        0.124    13.372 f  Core_cpu/U_RF/pc[27]_i_4/O
                         net (fo=1, routed)           0.433    13.805    Core_cpu/U_RF/pc[27]_i_4_n_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I2_O)        0.124    13.929 f  Core_cpu/U_RF/pc[27]_i_2/O
                         net (fo=6, routed)           1.361    15.291    Core_cpu/U_RF/Bus_addr__0[27]
    SLICE_X44Y56         LUT5 (Prop_lut5_I0_O)        0.152    15.443 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.849    16.292    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I1_O)        0.360    16.652 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.452    17.104    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I0_O)        0.326    17.430 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.700    18.130    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X37Y53         LUT2 (Prop_lut2_I1_O)        0.124    18.254 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.364    19.617    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X15Y64         LUT2 (Prop_lut2_I0_O)        0.124    19.741 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9472_9727_0_0_i_2/O
                         net (fo=6, routed)           0.930    20.671    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9472_9727_0_0_i_2_n_0
    SLICE_X13Y75         LUT6 (Prop_lut6_I5_O)        0.124    20.795 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_0_0_i_1/O
                         net (fo=128, routed)         5.018    25.813    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/WE
    SLICE_X64Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_C/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.518    40.427    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/WCLK
    SLICE_X64Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_C/CLK
                         clock pessimism             -0.163    40.264    
                         clock uncertainty           -0.180    40.084    
    SLICE_X64Y8          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.551    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_C
  -------------------------------------------------------------------
                         required time                         39.551    
                         arrival time                         -25.813    
  -------------------------------------------------------------------
                         slack                                 13.738    

Slack (MET) :             13.738ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_D/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        25.576ns  (logic 4.754ns (18.588%)  route 20.822ns (81.412%))
  Logic Levels:           23  (CARRY4=4 LUT2=4 LUT3=3 LUT4=2 LUT5=4 LUT6=6)
  Clock Path Skew:        0.027ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.427ns = ( 40.427 - 40.000 ) 
    Source Clock Delay      (SCD):    0.237ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.571     0.237    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X49Y47         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y47         FDCE (Prop_fdce_C_Q)         0.456     0.693 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.915     2.608    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X62Y45         LUT6 (Prop_lut6_I1_O)        0.124     2.732 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3/O
                         net (fo=1, routed)           0.640     3.372    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3_n_0
    SLICE_X62Y45         LUT6 (Prop_lut6_I3_O)        0.124     3.496 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1/O
                         net (fo=1, routed)           0.452     3.948    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1_n_0
    SLICE_X61Y45         LUT5 (Prop_lut5_I2_O)        0.124     4.072 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0/O
                         net (fo=19, routed)          0.762     4.833    Core_cpu/U_PC/spo[6]
    SLICE_X59Y49         LUT5 (Prop_lut5_I3_O)        0.124     4.957 r  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.263     5.220    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X59Y49         LUT5 (Prop_lut5_I4_O)        0.124     5.344 r  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          1.099     6.443    Core_cpu/U_PC/sext_op[2]
    SLICE_X51Y49         LUT3 (Prop_lut3_I2_O)        0.124     6.567 f  Core_cpu/U_PC/npc0_carry__2_i_6/O
                         net (fo=37, routed)          0.891     7.458    Core_cpu/U_RF/pc[31]_i_14
    SLICE_X47Y55         LUT3 (Prop_lut3_I0_O)        0.119     7.577 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.994     8.571    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X47Y50         LUT6 (Prop_lut6_I1_O)        0.332     8.903 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.785     9.688    Core_cpu/U_RF/sext_ext[13]
    SLICE_X49Y61         LUT4 (Prop_lut4_I0_O)        0.124     9.812 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.945    10.757    Core_cpu/U_RF/alu_B[13]
    SLICE_X47Y62         LUT3 (Prop_lut3_I2_O)        0.124    10.881 r  Core_cpu/U_RF/i__carry__2_i_5__1/O
                         net (fo=2, routed)           0.000    10.881    Core_cpu/U_ALU/Mem_DRAM_i_121_1[1]
    SLICE_X47Y62         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    11.431 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.431    Core_cpu/U_ALU/C0_inferred__0/i__carry__2_n_0
    SLICE_X47Y63         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.545 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.545    Core_cpu/U_ALU/C0_inferred__0/i__carry__3_n_0
    SLICE_X47Y64         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.659 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__4/CO[3]
                         net (fo=1, routed)           0.000    11.659    Core_cpu/U_ALU/C0_inferred__0/i__carry__4_n_0
    SLICE_X47Y65         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.313    11.972 f  Core_cpu/U_ALU/C0_inferred__0/i__carry__5/O[3]
                         net (fo=1, routed)           0.487    12.459    Core_cpu/U_ALU/data1[27]
    SLICE_X44Y65         LUT4 (Prop_lut4_I0_O)        0.306    12.765 f  Core_cpu/U_ALU/pc[27]_i_7/O
                         net (fo=1, routed)           0.484    13.248    Core_cpu/U_RF/pc[27]_i_2_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I4_O)        0.124    13.372 f  Core_cpu/U_RF/pc[27]_i_4/O
                         net (fo=1, routed)           0.433    13.805    Core_cpu/U_RF/pc[27]_i_4_n_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I2_O)        0.124    13.929 f  Core_cpu/U_RF/pc[27]_i_2/O
                         net (fo=6, routed)           1.361    15.291    Core_cpu/U_RF/Bus_addr__0[27]
    SLICE_X44Y56         LUT5 (Prop_lut5_I0_O)        0.152    15.443 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.849    16.292    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I1_O)        0.360    16.652 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.452    17.104    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I0_O)        0.326    17.430 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.700    18.130    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X37Y53         LUT2 (Prop_lut2_I1_O)        0.124    18.254 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.364    19.617    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X15Y64         LUT2 (Prop_lut2_I0_O)        0.124    19.741 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9472_9727_0_0_i_2/O
                         net (fo=6, routed)           0.930    20.671    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_9472_9727_0_0_i_2_n_0
    SLICE_X13Y75         LUT6 (Prop_lut6_I5_O)        0.124    20.795 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_0_0_i_1/O
                         net (fo=128, routed)         5.018    25.813    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/WE
    SLICE_X64Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_D/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.518    40.427    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/WCLK
    SLICE_X64Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_D/CLK
                         clock pessimism             -0.163    40.264    
                         clock uncertainty           -0.180    40.084    
    SLICE_X64Y8          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.551    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12800_13055_1_1/RAMS64E_D
  -------------------------------------------------------------------
                         required time                         39.551    
                         arrival time                         -25.813    
  -------------------------------------------------------------------
                         slack                                 13.738    

Slack (MET) :             13.764ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_A/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        25.543ns  (logic 4.982ns (19.504%)  route 20.561ns (80.496%))
  Logic Levels:           23  (CARRY4=4 LUT2=4 LUT3=3 LUT4=2 LUT5=4 LUT6=6)
  Clock Path Skew:        0.020ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.420ns = ( 40.420 - 40.000 ) 
    Source Clock Delay      (SCD):    0.237ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.571     0.237    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X49Y47         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y47         FDCE (Prop_fdce_C_Q)         0.456     0.693 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.915     2.608    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X62Y45         LUT6 (Prop_lut6_I1_O)        0.124     2.732 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3/O
                         net (fo=1, routed)           0.640     3.372    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3_n_0
    SLICE_X62Y45         LUT6 (Prop_lut6_I3_O)        0.124     3.496 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1/O
                         net (fo=1, routed)           0.452     3.948    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1_n_0
    SLICE_X61Y45         LUT5 (Prop_lut5_I2_O)        0.124     4.072 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0/O
                         net (fo=19, routed)          0.762     4.833    Core_cpu/U_PC/spo[6]
    SLICE_X59Y49         LUT5 (Prop_lut5_I3_O)        0.124     4.957 r  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.263     5.220    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X59Y49         LUT5 (Prop_lut5_I4_O)        0.124     5.344 r  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          1.099     6.443    Core_cpu/U_PC/sext_op[2]
    SLICE_X51Y49         LUT3 (Prop_lut3_I2_O)        0.124     6.567 f  Core_cpu/U_PC/npc0_carry__2_i_6/O
                         net (fo=37, routed)          0.891     7.458    Core_cpu/U_RF/pc[31]_i_14
    SLICE_X47Y55         LUT3 (Prop_lut3_I0_O)        0.119     7.577 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.994     8.571    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X47Y50         LUT6 (Prop_lut6_I1_O)        0.332     8.903 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.785     9.688    Core_cpu/U_RF/sext_ext[13]
    SLICE_X49Y61         LUT4 (Prop_lut4_I0_O)        0.124     9.812 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.945    10.757    Core_cpu/U_RF/alu_B[13]
    SLICE_X47Y62         LUT3 (Prop_lut3_I2_O)        0.124    10.881 r  Core_cpu/U_RF/i__carry__2_i_5__1/O
                         net (fo=2, routed)           0.000    10.881    Core_cpu/U_ALU/Mem_DRAM_i_121_1[1]
    SLICE_X47Y62         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    11.431 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.431    Core_cpu/U_ALU/C0_inferred__0/i__carry__2_n_0
    SLICE_X47Y63         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.545 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.545    Core_cpu/U_ALU/C0_inferred__0/i__carry__3_n_0
    SLICE_X47Y64         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.659 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__4/CO[3]
                         net (fo=1, routed)           0.000    11.659    Core_cpu/U_ALU/C0_inferred__0/i__carry__4_n_0
    SLICE_X47Y65         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.313    11.972 f  Core_cpu/U_ALU/C0_inferred__0/i__carry__5/O[3]
                         net (fo=1, routed)           0.487    12.459    Core_cpu/U_ALU/data1[27]
    SLICE_X44Y65         LUT4 (Prop_lut4_I0_O)        0.306    12.765 f  Core_cpu/U_ALU/pc[27]_i_7/O
                         net (fo=1, routed)           0.484    13.248    Core_cpu/U_RF/pc[27]_i_2_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I4_O)        0.124    13.372 f  Core_cpu/U_RF/pc[27]_i_4/O
                         net (fo=1, routed)           0.433    13.805    Core_cpu/U_RF/pc[27]_i_4_n_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I2_O)        0.124    13.929 f  Core_cpu/U_RF/pc[27]_i_2/O
                         net (fo=6, routed)           1.361    15.291    Core_cpu/U_RF/Bus_addr__0[27]
    SLICE_X44Y56         LUT5 (Prop_lut5_I0_O)        0.152    15.443 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.849    16.292    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I1_O)        0.360    16.652 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.452    17.104    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I0_O)        0.326    17.430 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.700    18.130    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X37Y53         LUT2 (Prop_lut2_I1_O)        0.124    18.254 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.768    20.022    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X15Y77         LUT2 (Prop_lut2_I0_O)        0.150    20.172 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.326    20.498    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X15Y79         LUT6 (Prop_lut6_I5_O)        0.326    20.824 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_0_0_i_1/O
                         net (fo=128, routed)         4.957    25.780    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WE
    SLICE_X64Y17         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_A/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.511    40.420    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WCLK
    SLICE_X64Y17         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_A/CLK
                         clock pessimism             -0.163    40.257    
                         clock uncertainty           -0.180    40.077    
    SLICE_X64Y17         RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.544    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_A
  -------------------------------------------------------------------
                         required time                         39.544    
                         arrival time                         -25.780    
  -------------------------------------------------------------------
                         slack                                 13.764    

Slack (MET) :             13.764ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_B/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        25.543ns  (logic 4.982ns (19.504%)  route 20.561ns (80.496%))
  Logic Levels:           23  (CARRY4=4 LUT2=4 LUT3=3 LUT4=2 LUT5=4 LUT6=6)
  Clock Path Skew:        0.020ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.420ns = ( 40.420 - 40.000 ) 
    Source Clock Delay      (SCD):    0.237ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.571     0.237    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X49Y47         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y47         FDCE (Prop_fdce_C_Q)         0.456     0.693 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.915     2.608    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X62Y45         LUT6 (Prop_lut6_I1_O)        0.124     2.732 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3/O
                         net (fo=1, routed)           0.640     3.372    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3_n_0
    SLICE_X62Y45         LUT6 (Prop_lut6_I3_O)        0.124     3.496 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1/O
                         net (fo=1, routed)           0.452     3.948    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1_n_0
    SLICE_X61Y45         LUT5 (Prop_lut5_I2_O)        0.124     4.072 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0/O
                         net (fo=19, routed)          0.762     4.833    Core_cpu/U_PC/spo[6]
    SLICE_X59Y49         LUT5 (Prop_lut5_I3_O)        0.124     4.957 r  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.263     5.220    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X59Y49         LUT5 (Prop_lut5_I4_O)        0.124     5.344 r  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          1.099     6.443    Core_cpu/U_PC/sext_op[2]
    SLICE_X51Y49         LUT3 (Prop_lut3_I2_O)        0.124     6.567 f  Core_cpu/U_PC/npc0_carry__2_i_6/O
                         net (fo=37, routed)          0.891     7.458    Core_cpu/U_RF/pc[31]_i_14
    SLICE_X47Y55         LUT3 (Prop_lut3_I0_O)        0.119     7.577 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.994     8.571    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X47Y50         LUT6 (Prop_lut6_I1_O)        0.332     8.903 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.785     9.688    Core_cpu/U_RF/sext_ext[13]
    SLICE_X49Y61         LUT4 (Prop_lut4_I0_O)        0.124     9.812 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.945    10.757    Core_cpu/U_RF/alu_B[13]
    SLICE_X47Y62         LUT3 (Prop_lut3_I2_O)        0.124    10.881 r  Core_cpu/U_RF/i__carry__2_i_5__1/O
                         net (fo=2, routed)           0.000    10.881    Core_cpu/U_ALU/Mem_DRAM_i_121_1[1]
    SLICE_X47Y62         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    11.431 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.431    Core_cpu/U_ALU/C0_inferred__0/i__carry__2_n_0
    SLICE_X47Y63         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.545 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.545    Core_cpu/U_ALU/C0_inferred__0/i__carry__3_n_0
    SLICE_X47Y64         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.659 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__4/CO[3]
                         net (fo=1, routed)           0.000    11.659    Core_cpu/U_ALU/C0_inferred__0/i__carry__4_n_0
    SLICE_X47Y65         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.313    11.972 f  Core_cpu/U_ALU/C0_inferred__0/i__carry__5/O[3]
                         net (fo=1, routed)           0.487    12.459    Core_cpu/U_ALU/data1[27]
    SLICE_X44Y65         LUT4 (Prop_lut4_I0_O)        0.306    12.765 f  Core_cpu/U_ALU/pc[27]_i_7/O
                         net (fo=1, routed)           0.484    13.248    Core_cpu/U_RF/pc[27]_i_2_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I4_O)        0.124    13.372 f  Core_cpu/U_RF/pc[27]_i_4/O
                         net (fo=1, routed)           0.433    13.805    Core_cpu/U_RF/pc[27]_i_4_n_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I2_O)        0.124    13.929 f  Core_cpu/U_RF/pc[27]_i_2/O
                         net (fo=6, routed)           1.361    15.291    Core_cpu/U_RF/Bus_addr__0[27]
    SLICE_X44Y56         LUT5 (Prop_lut5_I0_O)        0.152    15.443 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.849    16.292    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I1_O)        0.360    16.652 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.452    17.104    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I0_O)        0.326    17.430 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.700    18.130    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X37Y53         LUT2 (Prop_lut2_I1_O)        0.124    18.254 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.768    20.022    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X15Y77         LUT2 (Prop_lut2_I0_O)        0.150    20.172 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.326    20.498    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X15Y79         LUT6 (Prop_lut6_I5_O)        0.326    20.824 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_0_0_i_1/O
                         net (fo=128, routed)         4.957    25.780    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WE
    SLICE_X64Y17         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_B/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.511    40.420    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WCLK
    SLICE_X64Y17         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_B/CLK
                         clock pessimism             -0.163    40.257    
                         clock uncertainty           -0.180    40.077    
    SLICE_X64Y17         RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.544    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_B
  -------------------------------------------------------------------
                         required time                         39.544    
                         arrival time                         -25.780    
  -------------------------------------------------------------------
                         slack                                 13.764    

Slack (MET) :             13.764ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_C/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        25.543ns  (logic 4.982ns (19.504%)  route 20.561ns (80.496%))
  Logic Levels:           23  (CARRY4=4 LUT2=4 LUT3=3 LUT4=2 LUT5=4 LUT6=6)
  Clock Path Skew:        0.020ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.420ns = ( 40.420 - 40.000 ) 
    Source Clock Delay      (SCD):    0.237ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.571     0.237    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X49Y47         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y47         FDCE (Prop_fdce_C_Q)         0.456     0.693 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.915     2.608    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X62Y45         LUT6 (Prop_lut6_I1_O)        0.124     2.732 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3/O
                         net (fo=1, routed)           0.640     3.372    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3_n_0
    SLICE_X62Y45         LUT6 (Prop_lut6_I3_O)        0.124     3.496 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1/O
                         net (fo=1, routed)           0.452     3.948    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1_n_0
    SLICE_X61Y45         LUT5 (Prop_lut5_I2_O)        0.124     4.072 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0/O
                         net (fo=19, routed)          0.762     4.833    Core_cpu/U_PC/spo[6]
    SLICE_X59Y49         LUT5 (Prop_lut5_I3_O)        0.124     4.957 r  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.263     5.220    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X59Y49         LUT5 (Prop_lut5_I4_O)        0.124     5.344 r  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          1.099     6.443    Core_cpu/U_PC/sext_op[2]
    SLICE_X51Y49         LUT3 (Prop_lut3_I2_O)        0.124     6.567 f  Core_cpu/U_PC/npc0_carry__2_i_6/O
                         net (fo=37, routed)          0.891     7.458    Core_cpu/U_RF/pc[31]_i_14
    SLICE_X47Y55         LUT3 (Prop_lut3_I0_O)        0.119     7.577 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.994     8.571    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X47Y50         LUT6 (Prop_lut6_I1_O)        0.332     8.903 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.785     9.688    Core_cpu/U_RF/sext_ext[13]
    SLICE_X49Y61         LUT4 (Prop_lut4_I0_O)        0.124     9.812 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.945    10.757    Core_cpu/U_RF/alu_B[13]
    SLICE_X47Y62         LUT3 (Prop_lut3_I2_O)        0.124    10.881 r  Core_cpu/U_RF/i__carry__2_i_5__1/O
                         net (fo=2, routed)           0.000    10.881    Core_cpu/U_ALU/Mem_DRAM_i_121_1[1]
    SLICE_X47Y62         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    11.431 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.431    Core_cpu/U_ALU/C0_inferred__0/i__carry__2_n_0
    SLICE_X47Y63         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.545 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.545    Core_cpu/U_ALU/C0_inferred__0/i__carry__3_n_0
    SLICE_X47Y64         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.659 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__4/CO[3]
                         net (fo=1, routed)           0.000    11.659    Core_cpu/U_ALU/C0_inferred__0/i__carry__4_n_0
    SLICE_X47Y65         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.313    11.972 f  Core_cpu/U_ALU/C0_inferred__0/i__carry__5/O[3]
                         net (fo=1, routed)           0.487    12.459    Core_cpu/U_ALU/data1[27]
    SLICE_X44Y65         LUT4 (Prop_lut4_I0_O)        0.306    12.765 f  Core_cpu/U_ALU/pc[27]_i_7/O
                         net (fo=1, routed)           0.484    13.248    Core_cpu/U_RF/pc[27]_i_2_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I4_O)        0.124    13.372 f  Core_cpu/U_RF/pc[27]_i_4/O
                         net (fo=1, routed)           0.433    13.805    Core_cpu/U_RF/pc[27]_i_4_n_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I2_O)        0.124    13.929 f  Core_cpu/U_RF/pc[27]_i_2/O
                         net (fo=6, routed)           1.361    15.291    Core_cpu/U_RF/Bus_addr__0[27]
    SLICE_X44Y56         LUT5 (Prop_lut5_I0_O)        0.152    15.443 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.849    16.292    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I1_O)        0.360    16.652 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.452    17.104    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I0_O)        0.326    17.430 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.700    18.130    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X37Y53         LUT2 (Prop_lut2_I1_O)        0.124    18.254 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.768    20.022    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X15Y77         LUT2 (Prop_lut2_I0_O)        0.150    20.172 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.326    20.498    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X15Y79         LUT6 (Prop_lut6_I5_O)        0.326    20.824 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_0_0_i_1/O
                         net (fo=128, routed)         4.957    25.780    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WE
    SLICE_X64Y17         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_C/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.511    40.420    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WCLK
    SLICE_X64Y17         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_C/CLK
                         clock pessimism             -0.163    40.257    
                         clock uncertainty           -0.180    40.077    
    SLICE_X64Y17         RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.544    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_C
  -------------------------------------------------------------------
                         required time                         39.544    
                         arrival time                         -25.780    
  -------------------------------------------------------------------
                         slack                                 13.764    

Slack (MET) :             13.764ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_D/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        25.543ns  (logic 4.982ns (19.504%)  route 20.561ns (80.496%))
  Logic Levels:           23  (CARRY4=4 LUT2=4 LUT3=3 LUT4=2 LUT5=4 LUT6=6)
  Clock Path Skew:        0.020ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.420ns = ( 40.420 - 40.000 ) 
    Source Clock Delay      (SCD):    0.237ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.571     0.237    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X49Y47         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y47         FDCE (Prop_fdce_C_Q)         0.456     0.693 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.915     2.608    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X62Y45         LUT6 (Prop_lut6_I1_O)        0.124     2.732 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3/O
                         net (fo=1, routed)           0.640     3.372    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3_n_0
    SLICE_X62Y45         LUT6 (Prop_lut6_I3_O)        0.124     3.496 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1/O
                         net (fo=1, routed)           0.452     3.948    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1_n_0
    SLICE_X61Y45         LUT5 (Prop_lut5_I2_O)        0.124     4.072 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0/O
                         net (fo=19, routed)          0.762     4.833    Core_cpu/U_PC/spo[6]
    SLICE_X59Y49         LUT5 (Prop_lut5_I3_O)        0.124     4.957 r  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.263     5.220    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X59Y49         LUT5 (Prop_lut5_I4_O)        0.124     5.344 r  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          1.099     6.443    Core_cpu/U_PC/sext_op[2]
    SLICE_X51Y49         LUT3 (Prop_lut3_I2_O)        0.124     6.567 f  Core_cpu/U_PC/npc0_carry__2_i_6/O
                         net (fo=37, routed)          0.891     7.458    Core_cpu/U_RF/pc[31]_i_14
    SLICE_X47Y55         LUT3 (Prop_lut3_I0_O)        0.119     7.577 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.994     8.571    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X47Y50         LUT6 (Prop_lut6_I1_O)        0.332     8.903 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.785     9.688    Core_cpu/U_RF/sext_ext[13]
    SLICE_X49Y61         LUT4 (Prop_lut4_I0_O)        0.124     9.812 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.945    10.757    Core_cpu/U_RF/alu_B[13]
    SLICE_X47Y62         LUT3 (Prop_lut3_I2_O)        0.124    10.881 r  Core_cpu/U_RF/i__carry__2_i_5__1/O
                         net (fo=2, routed)           0.000    10.881    Core_cpu/U_ALU/Mem_DRAM_i_121_1[1]
    SLICE_X47Y62         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    11.431 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.431    Core_cpu/U_ALU/C0_inferred__0/i__carry__2_n_0
    SLICE_X47Y63         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.545 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.545    Core_cpu/U_ALU/C0_inferred__0/i__carry__3_n_0
    SLICE_X47Y64         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.659 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__4/CO[3]
                         net (fo=1, routed)           0.000    11.659    Core_cpu/U_ALU/C0_inferred__0/i__carry__4_n_0
    SLICE_X47Y65         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.313    11.972 f  Core_cpu/U_ALU/C0_inferred__0/i__carry__5/O[3]
                         net (fo=1, routed)           0.487    12.459    Core_cpu/U_ALU/data1[27]
    SLICE_X44Y65         LUT4 (Prop_lut4_I0_O)        0.306    12.765 f  Core_cpu/U_ALU/pc[27]_i_7/O
                         net (fo=1, routed)           0.484    13.248    Core_cpu/U_RF/pc[27]_i_2_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I4_O)        0.124    13.372 f  Core_cpu/U_RF/pc[27]_i_4/O
                         net (fo=1, routed)           0.433    13.805    Core_cpu/U_RF/pc[27]_i_4_n_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I2_O)        0.124    13.929 f  Core_cpu/U_RF/pc[27]_i_2/O
                         net (fo=6, routed)           1.361    15.291    Core_cpu/U_RF/Bus_addr__0[27]
    SLICE_X44Y56         LUT5 (Prop_lut5_I0_O)        0.152    15.443 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.849    16.292    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I1_O)        0.360    16.652 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.452    17.104    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I0_O)        0.326    17.430 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.700    18.130    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X37Y53         LUT2 (Prop_lut2_I1_O)        0.124    18.254 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          1.768    20.022    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X15Y77         LUT2 (Prop_lut2_I0_O)        0.150    20.172 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.326    20.498    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X15Y79         LUT6 (Prop_lut6_I5_O)        0.326    20.824 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_0_0_i_1/O
                         net (fo=128, routed)         4.957    25.780    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WE
    SLICE_X64Y17         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_D/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.511    40.420    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WCLK
    SLICE_X64Y17         RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_D/CLK
                         clock pessimism             -0.163    40.257    
                         clock uncertainty           -0.180    40.077    
    SLICE_X64Y17         RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.544    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_D
  -------------------------------------------------------------------
                         required time                         39.544    
                         arrival time                         -25.780    
  -------------------------------------------------------------------
                         slack                                 13.764    

Slack (MET) :             13.828ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/RAMS64E_A/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        25.420ns  (logic 4.630ns (18.214%)  route 20.790ns (81.786%))
  Logic Levels:           22  (CARRY4=4 LUT2=3 LUT3=3 LUT4=2 LUT5=4 LUT6=6)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.361ns = ( 40.361 - 40.000 ) 
    Source Clock Delay      (SCD):    0.237ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.571     0.237    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X49Y47         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y47         FDCE (Prop_fdce_C_Q)         0.456     0.693 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.915     2.608    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X62Y45         LUT6 (Prop_lut6_I1_O)        0.124     2.732 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3/O
                         net (fo=1, routed)           0.640     3.372    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3_n_0
    SLICE_X62Y45         LUT6 (Prop_lut6_I3_O)        0.124     3.496 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1/O
                         net (fo=1, routed)           0.452     3.948    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1_n_0
    SLICE_X61Y45         LUT5 (Prop_lut5_I2_O)        0.124     4.072 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0/O
                         net (fo=19, routed)          0.762     4.833    Core_cpu/U_PC/spo[6]
    SLICE_X59Y49         LUT5 (Prop_lut5_I3_O)        0.124     4.957 r  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.263     5.220    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X59Y49         LUT5 (Prop_lut5_I4_O)        0.124     5.344 r  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          1.099     6.443    Core_cpu/U_PC/sext_op[2]
    SLICE_X51Y49         LUT3 (Prop_lut3_I2_O)        0.124     6.567 f  Core_cpu/U_PC/npc0_carry__2_i_6/O
                         net (fo=37, routed)          0.891     7.458    Core_cpu/U_RF/pc[31]_i_14
    SLICE_X47Y55         LUT3 (Prop_lut3_I0_O)        0.119     7.577 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.994     8.571    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X47Y50         LUT6 (Prop_lut6_I1_O)        0.332     8.903 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.785     9.688    Core_cpu/U_RF/sext_ext[13]
    SLICE_X49Y61         LUT4 (Prop_lut4_I0_O)        0.124     9.812 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.945    10.757    Core_cpu/U_RF/alu_B[13]
    SLICE_X47Y62         LUT3 (Prop_lut3_I2_O)        0.124    10.881 r  Core_cpu/U_RF/i__carry__2_i_5__1/O
                         net (fo=2, routed)           0.000    10.881    Core_cpu/U_ALU/Mem_DRAM_i_121_1[1]
    SLICE_X47Y62         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    11.431 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.431    Core_cpu/U_ALU/C0_inferred__0/i__carry__2_n_0
    SLICE_X47Y63         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.545 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.545    Core_cpu/U_ALU/C0_inferred__0/i__carry__3_n_0
    SLICE_X47Y64         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.659 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__4/CO[3]
                         net (fo=1, routed)           0.000    11.659    Core_cpu/U_ALU/C0_inferred__0/i__carry__4_n_0
    SLICE_X47Y65         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.313    11.972 f  Core_cpu/U_ALU/C0_inferred__0/i__carry__5/O[3]
                         net (fo=1, routed)           0.487    12.459    Core_cpu/U_ALU/data1[27]
    SLICE_X44Y65         LUT4 (Prop_lut4_I0_O)        0.306    12.765 f  Core_cpu/U_ALU/pc[27]_i_7/O
                         net (fo=1, routed)           0.484    13.248    Core_cpu/U_RF/pc[27]_i_2_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I4_O)        0.124    13.372 f  Core_cpu/U_RF/pc[27]_i_4/O
                         net (fo=1, routed)           0.433    13.805    Core_cpu/U_RF/pc[27]_i_4_n_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I2_O)        0.124    13.929 f  Core_cpu/U_RF/pc[27]_i_2/O
                         net (fo=6, routed)           1.361    15.291    Core_cpu/U_RF/Bus_addr__0[27]
    SLICE_X44Y56         LUT5 (Prop_lut5_I0_O)        0.152    15.443 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.849    16.292    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I1_O)        0.360    16.652 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.452    17.104    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I0_O)        0.326    17.430 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.700    18.130    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X37Y53         LUT2 (Prop_lut2_I1_O)        0.124    18.254 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.103    20.356    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X36Y91         LUT6 (Prop_lut6_I1_O)        0.124    20.480 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_0_0_i_1/O
                         net (fo=128, routed)         5.177    25.657    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/WE
    SLICE_X54Y4          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/RAMS64E_A/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.452    40.361    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/WCLK
    SLICE_X54Y4          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/RAMS64E_A/CLK
                         clock pessimism             -0.163    40.198    
                         clock uncertainty           -0.180    40.018    
    SLICE_X54Y4          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.485    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/RAMS64E_A
  -------------------------------------------------------------------
                         required time                         39.485    
                         arrival time                         -25.657    
  -------------------------------------------------------------------
                         slack                                 13.828    

Slack (MET) :             13.828ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/RAMS64E_B/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        25.420ns  (logic 4.630ns (18.214%)  route 20.790ns (81.786%))
  Logic Levels:           22  (CARRY4=4 LUT2=3 LUT3=3 LUT4=2 LUT5=4 LUT6=6)
  Clock Path Skew:        -0.039ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.361ns = ( 40.361 - 40.000 ) 
    Source Clock Delay      (SCD):    0.237ns
    Clock Pessimism Removal (CPR):    -0.163ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.724    -2.274    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -2.150 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -1.430    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -1.334 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.571     0.237    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X49Y47         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y47         FDCE (Prop_fdce_C_Q)         0.456     0.693 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.915     2.608    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X62Y45         LUT6 (Prop_lut6_I1_O)        0.124     2.732 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3/O
                         net (fo=1, routed)           0.640     3.372    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_3_n_0
    SLICE_X62Y45         LUT6 (Prop_lut6_I3_O)        0.124     3.496 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1/O
                         net (fo=1, routed)           0.452     3.948    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0_i_1_n_0
    SLICE_X61Y45         LUT5 (Prop_lut5_I2_O)        0.124     4.072 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[6]_INST_0/O
                         net (fo=19, routed)          0.762     4.833    Core_cpu/U_PC/spo[6]
    SLICE_X59Y49         LUT5 (Prop_lut5_I3_O)        0.124     4.957 r  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.263     5.220    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X59Y49         LUT5 (Prop_lut5_I4_O)        0.124     5.344 r  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          1.099     6.443    Core_cpu/U_PC/sext_op[2]
    SLICE_X51Y49         LUT3 (Prop_lut3_I2_O)        0.124     6.567 f  Core_cpu/U_PC/npc0_carry__2_i_6/O
                         net (fo=37, routed)          0.891     7.458    Core_cpu/U_RF/pc[31]_i_14
    SLICE_X47Y55         LUT3 (Prop_lut3_I0_O)        0.119     7.577 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.994     8.571    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X47Y50         LUT6 (Prop_lut6_I1_O)        0.332     8.903 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.785     9.688    Core_cpu/U_RF/sext_ext[13]
    SLICE_X49Y61         LUT4 (Prop_lut4_I0_O)        0.124     9.812 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.945    10.757    Core_cpu/U_RF/alu_B[13]
    SLICE_X47Y62         LUT3 (Prop_lut3_I2_O)        0.124    10.881 r  Core_cpu/U_RF/i__carry__2_i_5__1/O
                         net (fo=2, routed)           0.000    10.881    Core_cpu/U_ALU/Mem_DRAM_i_121_1[1]
    SLICE_X47Y62         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    11.431 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__2/CO[3]
                         net (fo=1, routed)           0.000    11.431    Core_cpu/U_ALU/C0_inferred__0/i__carry__2_n_0
    SLICE_X47Y63         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.545 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__3/CO[3]
                         net (fo=1, routed)           0.000    11.545    Core_cpu/U_ALU/C0_inferred__0/i__carry__3_n_0
    SLICE_X47Y64         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    11.659 r  Core_cpu/U_ALU/C0_inferred__0/i__carry__4/CO[3]
                         net (fo=1, routed)           0.000    11.659    Core_cpu/U_ALU/C0_inferred__0/i__carry__4_n_0
    SLICE_X47Y65         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.313    11.972 f  Core_cpu/U_ALU/C0_inferred__0/i__carry__5/O[3]
                         net (fo=1, routed)           0.487    12.459    Core_cpu/U_ALU/data1[27]
    SLICE_X44Y65         LUT4 (Prop_lut4_I0_O)        0.306    12.765 f  Core_cpu/U_ALU/pc[27]_i_7/O
                         net (fo=1, routed)           0.484    13.248    Core_cpu/U_RF/pc[27]_i_2_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I4_O)        0.124    13.372 f  Core_cpu/U_RF/pc[27]_i_4/O
                         net (fo=1, routed)           0.433    13.805    Core_cpu/U_RF/pc[27]_i_4_n_0
    SLICE_X45Y67         LUT6 (Prop_lut6_I2_O)        0.124    13.929 f  Core_cpu/U_RF/pc[27]_i_2/O
                         net (fo=6, routed)           1.361    15.291    Core_cpu/U_RF/Bus_addr__0[27]
    SLICE_X44Y56         LUT5 (Prop_lut5_I0_O)        0.152    15.443 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.849    16.292    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I1_O)        0.360    16.652 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.452    17.104    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X44Y54         LUT2 (Prop_lut2_I0_O)        0.326    17.430 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.700    18.130    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X37Y53         LUT2 (Prop_lut2_I1_O)        0.124    18.254 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.103    20.356    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X36Y91         LUT6 (Prop_lut6_I1_O)        0.124    20.480 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_0_0_i_1/O
                         net (fo=128, routed)         5.177    25.657    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/WE
    SLICE_X54Y4          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/RAMS64E_B/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.572    38.080    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.180 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    38.818    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    38.909 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.452    40.361    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/WCLK
    SLICE_X54Y4          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/RAMS64E_B/CLK
                         clock pessimism             -0.163    40.198    
                         clock uncertainty           -0.180    40.018    
    SLICE_X54Y4          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.485    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11008_11263_12_12/RAMS64E_B
  -------------------------------------------------------------------
                         required time                         39.485    
                         arrival time                         -25.657    
  -------------------------------------------------------------------
                         slack                                 13.828    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.154ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[11]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[12]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.526ns  (logic 0.355ns (67.491%)  route 0.171ns (32.509%))
  Logic Levels:           3  (CARRY4=2 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.969ns
    Source Clock Delay      (SCD):    0.415ns
    Clock Pessimism Removal (CPR):    0.287ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.564     0.415    U_Timer/cpu_clk_BUFG
    SLICE_X39Y49         FDCE                                         r  U_Timer/counter1_reg[11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y49         FDCE (Prop_fdce_C_Q)         0.141     0.556 r  U_Timer/counter1_reg[11]/Q
                         net (fo=3, routed)           0.170     0.727    U_Timer/counter1_reg[11]
    SLICE_X39Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.772 r  U_Timer/counter1[8]_i_2/O
                         net (fo=1, routed)           0.000     0.772    U_Timer/counter1[8]_i_2_n_0
    SLICE_X39Y49         CARRY4 (Prop_carry4_S[3]_CO[3])
                                                      0.115     0.887 r  U_Timer/counter1_reg[8]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.887    U_Timer/counter1_reg[8]_i_1_n_0
    SLICE_X39Y50         CARRY4 (Prop_carry4_CI_O[0])
                                                      0.054     0.941 r  U_Timer/counter1_reg[12]_i_1/O[0]
                         net (fo=1, routed)           0.000     0.941    U_Timer/counter1_reg[12]_i_1_n_7
    SLICE_X39Y50         FDCE                                         r  U_Timer/counter1_reg[12]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.830     0.969    U_Timer/cpu_clk_BUFG
    SLICE_X39Y50         FDCE                                         r  U_Timer/counter1_reg[12]/C
                         clock pessimism             -0.287     0.682    
    SLICE_X39Y50         FDCE (Hold_fdce_C_D)         0.105     0.787    U_Timer/counter1_reg[12]
  -------------------------------------------------------------------
                         required time                         -0.787    
                         arrival time                           0.941    
  -------------------------------------------------------------------
                         slack                                  0.154    

Slack (MET) :             0.165ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[11]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[14]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.537ns  (logic 0.366ns (68.157%)  route 0.171ns (31.843%))
  Logic Levels:           3  (CARRY4=2 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.969ns
    Source Clock Delay      (SCD):    0.415ns
    Clock Pessimism Removal (CPR):    0.287ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.564     0.415    U_Timer/cpu_clk_BUFG
    SLICE_X39Y49         FDCE                                         r  U_Timer/counter1_reg[11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y49         FDCE (Prop_fdce_C_Q)         0.141     0.556 r  U_Timer/counter1_reg[11]/Q
                         net (fo=3, routed)           0.170     0.727    U_Timer/counter1_reg[11]
    SLICE_X39Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.772 r  U_Timer/counter1[8]_i_2/O
                         net (fo=1, routed)           0.000     0.772    U_Timer/counter1[8]_i_2_n_0
    SLICE_X39Y49         CARRY4 (Prop_carry4_S[3]_CO[3])
                                                      0.115     0.887 r  U_Timer/counter1_reg[8]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.887    U_Timer/counter1_reg[8]_i_1_n_0
    SLICE_X39Y50         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.065     0.952 r  U_Timer/counter1_reg[12]_i_1/O[2]
                         net (fo=1, routed)           0.000     0.952    U_Timer/counter1_reg[12]_i_1_n_5
    SLICE_X39Y50         FDCE                                         r  U_Timer/counter1_reg[14]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.830     0.969    U_Timer/cpu_clk_BUFG
    SLICE_X39Y50         FDCE                                         r  U_Timer/counter1_reg[14]/C
                         clock pessimism             -0.287     0.682    
    SLICE_X39Y50         FDCE (Hold_fdce_C_D)         0.105     0.787    U_Timer/counter1_reg[14]
  -------------------------------------------------------------------
                         required time                         -0.787    
                         arrival time                           0.952    
  -------------------------------------------------------------------
                         slack                                  0.165    

Slack (MET) :             0.190ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[11]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[13]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.562ns  (logic 0.391ns (69.574%)  route 0.171ns (30.426%))
  Logic Levels:           3  (CARRY4=2 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.969ns
    Source Clock Delay      (SCD):    0.415ns
    Clock Pessimism Removal (CPR):    0.287ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.564     0.415    U_Timer/cpu_clk_BUFG
    SLICE_X39Y49         FDCE                                         r  U_Timer/counter1_reg[11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y49         FDCE (Prop_fdce_C_Q)         0.141     0.556 r  U_Timer/counter1_reg[11]/Q
                         net (fo=3, routed)           0.170     0.727    U_Timer/counter1_reg[11]
    SLICE_X39Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.772 r  U_Timer/counter1[8]_i_2/O
                         net (fo=1, routed)           0.000     0.772    U_Timer/counter1[8]_i_2_n_0
    SLICE_X39Y49         CARRY4 (Prop_carry4_S[3]_CO[3])
                                                      0.115     0.887 r  U_Timer/counter1_reg[8]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.887    U_Timer/counter1_reg[8]_i_1_n_0
    SLICE_X39Y50         CARRY4 (Prop_carry4_CI_O[1])
                                                      0.090     0.977 r  U_Timer/counter1_reg[12]_i_1/O[1]
                         net (fo=1, routed)           0.000     0.977    U_Timer/counter1_reg[12]_i_1_n_6
    SLICE_X39Y50         FDCE                                         r  U_Timer/counter1_reg[13]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.830     0.969    U_Timer/cpu_clk_BUFG
    SLICE_X39Y50         FDCE                                         r  U_Timer/counter1_reg[13]/C
                         clock pessimism             -0.287     0.682    
    SLICE_X39Y50         FDCE (Hold_fdce_C_D)         0.105     0.787    U_Timer/counter1_reg[13]
  -------------------------------------------------------------------
                         required time                         -0.787    
                         arrival time                           0.977    
  -------------------------------------------------------------------
                         slack                                  0.190    

Slack (MET) :             0.190ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[11]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[15]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.562ns  (logic 0.391ns (69.574%)  route 0.171ns (30.426%))
  Logic Levels:           3  (CARRY4=2 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.969ns
    Source Clock Delay      (SCD):    0.415ns
    Clock Pessimism Removal (CPR):    0.287ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.564     0.415    U_Timer/cpu_clk_BUFG
    SLICE_X39Y49         FDCE                                         r  U_Timer/counter1_reg[11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y49         FDCE (Prop_fdce_C_Q)         0.141     0.556 r  U_Timer/counter1_reg[11]/Q
                         net (fo=3, routed)           0.170     0.727    U_Timer/counter1_reg[11]
    SLICE_X39Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.772 r  U_Timer/counter1[8]_i_2/O
                         net (fo=1, routed)           0.000     0.772    U_Timer/counter1[8]_i_2_n_0
    SLICE_X39Y49         CARRY4 (Prop_carry4_S[3]_CO[3])
                                                      0.115     0.887 r  U_Timer/counter1_reg[8]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.887    U_Timer/counter1_reg[8]_i_1_n_0
    SLICE_X39Y50         CARRY4 (Prop_carry4_CI_O[3])
                                                      0.090     0.977 r  U_Timer/counter1_reg[12]_i_1/O[3]
                         net (fo=1, routed)           0.000     0.977    U_Timer/counter1_reg[12]_i_1_n_4
    SLICE_X39Y50         FDCE                                         r  U_Timer/counter1_reg[15]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.830     0.969    U_Timer/cpu_clk_BUFG
    SLICE_X39Y50         FDCE                                         r  U_Timer/counter1_reg[15]/C
                         clock pessimism             -0.287     0.682    
    SLICE_X39Y50         FDCE (Hold_fdce_C_D)         0.105     0.787    U_Timer/counter1_reg[15]
  -------------------------------------------------------------------
                         required time                         -0.787    
                         arrival time                           0.977    
  -------------------------------------------------------------------
                         slack                                  0.190    

Slack (MET) :             0.193ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[11]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[16]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.565ns  (logic 0.394ns (69.735%)  route 0.171ns (30.265%))
  Logic Levels:           4  (CARRY4=3 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.969ns
    Source Clock Delay      (SCD):    0.415ns
    Clock Pessimism Removal (CPR):    0.287ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.564     0.415    U_Timer/cpu_clk_BUFG
    SLICE_X39Y49         FDCE                                         r  U_Timer/counter1_reg[11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y49         FDCE (Prop_fdce_C_Q)         0.141     0.556 r  U_Timer/counter1_reg[11]/Q
                         net (fo=3, routed)           0.170     0.727    U_Timer/counter1_reg[11]
    SLICE_X39Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.772 r  U_Timer/counter1[8]_i_2/O
                         net (fo=1, routed)           0.000     0.772    U_Timer/counter1[8]_i_2_n_0
    SLICE_X39Y49         CARRY4 (Prop_carry4_S[3]_CO[3])
                                                      0.115     0.887 r  U_Timer/counter1_reg[8]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.887    U_Timer/counter1_reg[8]_i_1_n_0
    SLICE_X39Y50         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.039     0.926 r  U_Timer/counter1_reg[12]_i_1/CO[3]
                         net (fo=1, routed)           0.000     0.926    U_Timer/counter1_reg[12]_i_1_n_0
    SLICE_X39Y51         CARRY4 (Prop_carry4_CI_O[0])
                                                      0.054     0.980 r  U_Timer/counter1_reg[16]_i_1/O[0]
                         net (fo=1, routed)           0.000     0.980    U_Timer/counter1_reg[16]_i_1_n_7
    SLICE_X39Y51         FDCE                                         r  U_Timer/counter1_reg[16]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.830     0.969    U_Timer/cpu_clk_BUFG
    SLICE_X39Y51         FDCE                                         r  U_Timer/counter1_reg[16]/C
                         clock pessimism             -0.287     0.682    
    SLICE_X39Y51         FDCE (Hold_fdce_C_D)         0.105     0.787    U_Timer/counter1_reg[16]
  -------------------------------------------------------------------
                         required time                         -0.787    
                         arrival time                           0.980    
  -------------------------------------------------------------------
                         slack                                  0.193    

Slack (MET) :             0.229ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][18]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.349ns  (logic 0.189ns (54.226%)  route 0.160ns (45.774%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.956ns
    Source Clock Delay      (SCD):    0.402ns
    Clock Pessimism Removal (CPR):    0.541ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.551     0.402    U_Button/cpu_clk_BUFG
    SLICE_X47Y24         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y24         FDCE (Prop_fdce_C_Q)         0.141     0.543 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.160     0.703    U_Button/p_1_in8_in
    SLICE_X45Y24         LUT4 (Prop_lut4_I0_O)        0.048     0.751 r  U_Button/debounce_gen[1].debounce_cnt[1][18]_i_1/O
                         net (fo=1, routed)           0.000     0.751    U_Button/debounce_gen[1].debounce_cnt[1][18]_i_1_n_0
    SLICE_X45Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][18]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.818     0.956    U_Button/cpu_clk_BUFG
    SLICE_X45Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][18]/C
                         clock pessimism             -0.541     0.415    
    SLICE_X45Y24         FDCE (Hold_fdce_C_D)         0.107     0.522    U_Button/debounce_gen[1].debounce_cnt_reg[1][18]
  -------------------------------------------------------------------
                         required time                         -0.522    
                         arrival time                           0.751    
  -------------------------------------------------------------------
                         slack                                  0.229    

Slack (MET) :             0.231ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][17]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.351ns  (logic 0.190ns (54.202%)  route 0.161ns (45.798%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.956ns
    Source Clock Delay      (SCD):    0.402ns
    Clock Pessimism Removal (CPR):    0.541ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.551     0.402    U_Button/cpu_clk_BUFG
    SLICE_X47Y24         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X47Y24         FDCE (Prop_fdce_C_Q)         0.141     0.543 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.161     0.704    U_Button/p_1_in8_in
    SLICE_X45Y24         LUT4 (Prop_lut4_I0_O)        0.049     0.753 r  U_Button/debounce_gen[1].debounce_cnt[1][17]_i_1/O
                         net (fo=1, routed)           0.000     0.753    U_Button/debounce_gen[1].debounce_cnt[1][17]_i_1_n_0
    SLICE_X45Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][17]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.818     0.956    U_Button/cpu_clk_BUFG
    SLICE_X45Y24         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][17]/C
                         clock pessimism             -0.541     0.415    
    SLICE_X45Y24         FDCE (Hold_fdce_C_D)         0.107     0.522    U_Button/debounce_gen[1].debounce_cnt_reg[1][17]
  -------------------------------------------------------------------
                         required time                         -0.522    
                         arrival time                           0.753    
  -------------------------------------------------------------------
                         slack                                  0.231    

Slack (MET) :             0.232ns  (arrival time - required time)
  Source:                 U_Timer/counter1_reg[11]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Timer/counter1_reg[20]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.604ns  (logic 0.433ns (71.689%)  route 0.171ns (28.311%))
  Logic Levels:           5  (CARRY4=4 LUT2=1)
  Clock Path Skew:        0.267ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.969ns
    Source Clock Delay      (SCD):    0.415ns
    Clock Pessimism Removal (CPR):    0.287ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.564     0.415    U_Timer/cpu_clk_BUFG
    SLICE_X39Y49         FDCE                                         r  U_Timer/counter1_reg[11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y49         FDCE (Prop_fdce_C_Q)         0.141     0.556 r  U_Timer/counter1_reg[11]/Q
                         net (fo=3, routed)           0.170     0.727    U_Timer/counter1_reg[11]
    SLICE_X39Y49         LUT2 (Prop_lut2_I0_O)        0.045     0.772 r  U_Timer/counter1[8]_i_2/O
                         net (fo=1, routed)           0.000     0.772    U_Timer/counter1[8]_i_2_n_0
    SLICE_X39Y49         CARRY4 (Prop_carry4_S[3]_CO[3])
                                                      0.115     0.887 r  U_Timer/counter1_reg[8]_i_1/CO[3]
                         net (fo=1, routed)           0.001     0.887    U_Timer/counter1_reg[8]_i_1_n_0
    SLICE_X39Y50         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.039     0.926 r  U_Timer/counter1_reg[12]_i_1/CO[3]
                         net (fo=1, routed)           0.000     0.926    U_Timer/counter1_reg[12]_i_1_n_0
    SLICE_X39Y51         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.039     0.965 r  U_Timer/counter1_reg[16]_i_1/CO[3]
                         net (fo=1, routed)           0.000     0.965    U_Timer/counter1_reg[16]_i_1_n_0
    SLICE_X39Y52         CARRY4 (Prop_carry4_CI_O[0])
                                                      0.054     1.019 r  U_Timer/counter1_reg[20]_i_1/O[0]
                         net (fo=1, routed)           0.000     1.019    U_Timer/counter1_reg[20]_i_1_n_7
    SLICE_X39Y52         FDCE                                         r  U_Timer/counter1_reg[20]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.830     0.969    U_Timer/cpu_clk_BUFG
    SLICE_X39Y52         FDCE                                         r  U_Timer/counter1_reg[20]/C
                         clock pessimism             -0.287     0.682    
    SLICE_X39Y52         FDCE (Hold_fdce_C_D)         0.105     0.787    U_Timer/counter1_reg[20]
  -------------------------------------------------------------------
                         required time                         -0.787    
                         arrival time                           1.019    
  -------------------------------------------------------------------
                         slack                                  0.232    

Slack (MET) :             0.236ns  (arrival time - required time)
  Source:                 U_Button/debounce_gen[3].button_stable_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[3].debounce_cnt_reg[3][5]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.358ns  (logic 0.189ns (52.761%)  route 0.169ns (47.239%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.015ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.960ns
    Source Clock Delay      (SCD):    0.404ns
    Clock Pessimism Removal (CPR):    0.541ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.553     0.404    U_Button/cpu_clk_BUFG
    SLICE_X41Y28         FDCE                                         r  U_Button/debounce_gen[3].button_stable_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X41Y28         FDCE (Prop_fdce_C_Q)         0.141     0.545 r  U_Button/debounce_gen[3].button_stable_reg[3]/Q
                         net (fo=22, routed)          0.169     0.714    U_Button/data0[3]
    SLICE_X41Y29         LUT4 (Prop_lut4_I1_O)        0.048     0.762 r  U_Button/debounce_gen[3].debounce_cnt[3][5]_i_1/O
                         net (fo=1, routed)           0.000     0.762    U_Button/debounce_gen[3].debounce_cnt[3][5]_i_1_n_0
    SLICE_X41Y29         FDCE                                         r  U_Button/debounce_gen[3].debounce_cnt_reg[3][5]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.822     0.960    U_Button/cpu_clk_BUFG
    SLICE_X41Y29         FDCE                                         r  U_Button/debounce_gen[3].debounce_cnt_reg[3][5]/C
                         clock pessimism             -0.541     0.419    
    SLICE_X41Y29         FDCE (Hold_fdce_C_D)         0.107     0.526    U_Button/debounce_gen[3].debounce_cnt_reg[3][5]
  -------------------------------------------------------------------
                         required time                         -0.526    
                         arrival time                           0.762    
  -------------------------------------------------------------------
                         slack                                  0.236    

Slack (MET) :             0.238ns  (arrival time - required time)
  Source:                 U_Button/debounce_gen[3].button_stable_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[3].debounce_cnt_reg[3][8]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.360ns  (logic 0.190ns (52.745%)  route 0.170ns (47.255%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.015ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    0.960ns
    Source Clock Delay      (SCD):    0.404ns
    Clock Pessimism Removal (CPR):    0.541ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.624    -0.485    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.440 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.174    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026    -0.148 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.553     0.404    U_Button/cpu_clk_BUFG
    SLICE_X41Y28         FDCE                                         r  U_Button/debounce_gen[3].button_stable_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X41Y28         FDCE (Prop_fdce_C_Q)         0.141     0.545 r  U_Button/debounce_gen[3].button_stable_reg[3]/Q
                         net (fo=22, routed)          0.170     0.715    U_Button/data0[3]
    SLICE_X41Y29         LUT4 (Prop_lut4_I1_O)        0.049     0.764 r  U_Button/debounce_gen[3].debounce_cnt[3][8]_i_1/O
                         net (fo=1, routed)           0.000     0.764    U_Button/debounce_gen[3].debounce_cnt[3][8]_i_1_n_0
    SLICE_X41Y29         FDCE                                         r  U_Button/debounce_gen[3].debounce_cnt_reg[3][8]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.902    -0.246    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056    -0.190 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.109    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.138 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.822     0.960    U_Button/cpu_clk_BUFG
    SLICE_X41Y29         FDCE                                         r  U_Button/debounce_gen[3].debounce_cnt_reg[3][8]/C
                         clock pessimism             -0.541     0.419    
    SLICE_X41Y29         FDCE (Hold_fdce_C_D)         0.107     0.526    U_Button/debounce_gen[3].debounce_cnt_reg[3][8]
  -------------------------------------------------------------------
                         required time                         -0.526    
                         arrival time                           0.764    
  -------------------------------------------------------------------
                         slack                                  0.238    





Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clk_out1_cpuclk
Waveform(ns):       { 0.000 20.000 }
Period(ns):         40.000
Sources:            { Clkgen/inst/plle2_adv_inst/CLKOUT0 }

Check Type        Corner  Lib Pin            Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     BUFG/I             n/a            2.155         40.000      37.845     BUFGCTRL_X0Y2   Clkgen/inst/clkout1_buf/I
Min Period        n/a     BUFG/I             n/a            2.155         40.000      37.845     BUFGCTRL_X0Y0   cpu_clk_BUFG_inst/I
Min Period        n/a     PLLE2_ADV/CLKOUT0  n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKOUT0
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X48Y47    Core_cpu/U_PC/pc_reg[0]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X48Y47    Core_cpu/U_PC/pc_reg[10]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X47Y50    Core_cpu/U_PC/pc_reg[11]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X51Y51    Core_cpu/U_PC/pc_reg[12]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X51Y51    Core_cpu/U_PC/pc_reg[13]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X51Y51    Core_cpu/U_PC/pc_reg[14]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X47Y51    Core_cpu/U_PC/pc_reg[15]/C
Max Period        n/a     PLLE2_ADV/CLKOUT0  n/a            160.000       40.000      120.000    PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKOUT0
Low Pulse Width   Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X52Y6     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_A/CLK
Low Pulse Width   Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X52Y6     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_B/CLK
Low Pulse Width   Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X52Y6     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_C/CLK
Low Pulse Width   Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X52Y6     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_D/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X6Y48     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_4_4/RAMS64E_A/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X6Y48     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_4_4/RAMS64E_B/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X6Y48     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_13056_13311_4_4/RAMS64E_C/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y33    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14336_14591_14_14/RAMS64E_A/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y33    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14336_14591_14_14/RAMS64E_B/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y33    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14336_14591_14_14/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X64Y71    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_30_30/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X64Y71    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_30_30/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X64Y71    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_30_30/RAMS64E_D/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X8Y64     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_31_31/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X8Y64     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_31_31/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X8Y64     Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_31_31/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X52Y109   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_22_22/RAMS64E_A/CLK
High Pulse Width  Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X52Y109   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_22_22/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X52Y109   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_22_22/RAMS64E_B/CLK
High Pulse Width  Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X52Y109   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_22_22/RAMS64E_B/CLK



---------------------------------------------------------------------------------------------------
From Clock:  clkfbout_cpuclk
  To Clock:  clkfbout_cpuclk

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack       12.633ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clkfbout_cpuclk
Waveform(ns):       { 0.000 20.000 }
Period(ns):         40.000
Sources:            { Clkgen/inst/plle2_adv_inst/CLKFBOUT }

Check Type  Corner  Lib Pin             Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period  n/a     BUFG/I              n/a            2.155         40.000      37.845     BUFGCTRL_X0Y1   Clkgen/inst/clkf_buf/I
Min Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBOUT
Min Period  n/a     PLLE2_ADV/CLKFBIN   n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBIN   n/a            52.633        40.000      12.633     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            160.000       40.000      120.000    PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBOUT



