Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat Jul  5 09:27:47 2025
| Host         : L running 64-bit major release  (build 9200)
| Command      : report_timing_summary -max_paths 10 -file miniRV_SoC_timing_summary_routed.rpt -pb miniRV_SoC_timing_summary_routed.pb -rpx miniRV_SoC_timing_summary_routed.rpx -warn_on_violation
| Design       : miniRV_SoC
| Device       : 7a35t-csg324
| Speed File   : -1  PRODUCTION 1.23 2018-06-13
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Timing Summary Report

------------------------------------------------------------------------------------------------
| Timer Settings
| --------------
------------------------------------------------------------------------------------------------

  Enable Multi Corner Analysis               :  Yes
  Enable Pessimism Removal                   :  Yes
  Pessimism Removal Resolution               :  Nearest Common Node
  Enable Input Delay Default Clock           :  No
  Enable Preset / Clear Arcs                 :  No
  Disable Flight Delays                      :  No
  Ignore I/O Paths                           :  No
  Timing Early Launch at Borrowing Latches   :  false

  Corner  Analyze    Analyze    
  Name    Max Paths  Min Paths  
  ------  ---------  ---------  
  Slow    Yes        Yes        
  Fast    Yes        Yes        



check_timing report

Table of Contents
-----------------
1. checking no_clock
2. checking constant_clock
3. checking pulse_width_clock
4. checking unconstrained_internal_endpoints
5. checking no_input_delay
6. checking no_output_delay
7. checking multiple_clock
8. checking generated_clocks
9. checking loops
10. checking partial_input_delay
11. checking partial_output_delay
12. checking latch_loops

1. checking no_clock
--------------------
 There are 0 register/latch pins with no clock.


2. checking constant_clock
--------------------------
 There are 0 register/latch pins with constant_clock.


3. checking pulse_width_clock
-----------------------------
 There are 0 register/latch pins which need pulse_width check


4. checking unconstrained_internal_endpoints
--------------------------------------------
 There are 0 pins that are not constrained for maximum delay.

 There are 0 pins that are not constrained for maximum delay due to constant clock.


5. checking no_input_delay
--------------------------
 There are 22 input ports with no input delay specified. (HIGH)

 There are 0 input ports with no input delay but user has a false path constraint.


6. checking no_output_delay
---------------------------
 There are 38 ports with no output delay specified. (HIGH)

 There are 0 ports with no output delay but user has a false path constraint

 There are 0 ports with no output delay but with a timing clock defined on it or propagating through it


7. checking multiple_clock
--------------------------
 There are 0 register/latch pins with multiple clocks.


8. checking generated_clocks
----------------------------
 There are 0 generated clocks that are not connected to a clock source.


9. checking loops
-----------------
 There are 0 combinational loops in the design.


10. checking partial_input_delay
--------------------------------
 There are 0 input ports with partial input delay specified.


11. checking partial_output_delay
---------------------------------
 There are 0 ports with partial output delay specified.


12. checking latch_loops
------------------------
 There are 0 combinational latch loops in the design through latch input



------------------------------------------------------------------------------------------------
| Design Timing Summary
| ---------------------
------------------------------------------------------------------------------------------------

    WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
    -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
     11.657        0.000                      0                82977        0.200        0.000                      0                82977        3.000        0.000                       0                  8605  


All user specified timing constraints are met.


------------------------------------------------------------------------------------------------
| Clock Summary
| -------------
------------------------------------------------------------------------------------------------

Clock              Waveform(ns)       Period(ns)      Frequency(MHz)
-----              ------------       ----------      --------------
fpga_clk           {0.000 5.000}      10.000          100.000         
  clk_out1_cpuclk  {0.000 20.000}     40.000          25.000          
  clkfbout_cpuclk  {0.000 20.000}     40.000          25.000          


------------------------------------------------------------------------------------------------
| Intra Clock Table
| -----------------
------------------------------------------------------------------------------------------------

Clock                  WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
-----                  -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
fpga_clk                                                                                                                                                             3.000        0.000                       0                     1  
  clk_out1_cpuclk       11.657        0.000                      0                82977        0.200        0.000                      0                82977       18.750        0.000                       0                  8601  
  clkfbout_cpuclk                                                                                                                                                   12.633        0.000                       0                     3  


------------------------------------------------------------------------------------------------
| Inter Clock Table
| -----------------
------------------------------------------------------------------------------------------------

From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  


------------------------------------------------------------------------------------------------
| Other Path Groups Table
| -----------------------
------------------------------------------------------------------------------------------------

Path Group    From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    ----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  


------------------------------------------------------------------------------------------------
| Timing Details
| --------------
------------------------------------------------------------------------------------------------


---------------------------------------------------------------------------------------------------
From Clock:  fpga_clk
  To Clock:  fpga_clk

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack        3.000ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         fpga_clk
Waveform(ns):       { 0.000 5.000 }
Period(ns):         10.000
Sources:            { fpga_clk }

Check Type        Corner  Lib Pin           Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     PLLE2_ADV/CLKIN1  n/a            1.249         10.000      8.751      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Max Period        n/a     PLLE2_ADV/CLKIN1  n/a            52.633        10.000      42.633     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Slow    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Fast    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Slow    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Fast    PLLE2_ADV/CLKIN1  n/a            2.000         5.000       3.000      PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKIN1



---------------------------------------------------------------------------------------------------
From Clock:  clk_out1_cpuclk
  To Clock:  clk_out1_cpuclk

Setup :            0  Failing Endpoints,  Worst Slack       11.657ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.200ns,  Total Violation        0.000ns
PW    :            0  Failing Endpoints,  Worst Slack       18.750ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             11.657ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_A/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.539ns  (logic 5.340ns (19.391%)  route 22.199ns (80.609%))
  Logic Levels:           23  (CARRY4=5 LUT2=5 LUT3=3 LUT4=1 LUT5=4 LUT6=5)
  Clock Path Skew:        -0.092ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.776ns = ( 40.776 - 40.000 ) 
    Source Clock Delay      (SCD):    0.724ns
    Clock Pessimism Removal (CPR):    -0.144ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.236    -1.762    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.638 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -0.917    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -0.821 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.545     0.724    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X51Y78         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y78         FDCE (Prop_fdce_C_Q)         0.456     1.180 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.668     2.848    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     2.972 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.405    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2_n_0
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     3.529 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1/O
                         net (fo=1, routed)           0.505     4.034    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1_n_0
    SLICE_X58Y84         LUT5 (Prop_lut5_I2_O)        0.124     4.158 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0/O
                         net (fo=7, routed)           1.689     5.847    Core_cpu/U_PC/spo[12]
    SLICE_X47Y89         LUT5 (Prop_lut5_I0_O)        0.124     5.971 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.433     6.404    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X47Y89         LUT5 (Prop_lut5_I4_O)        0.124     6.528 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.858     7.386    Core_cpu/U_PC/sext_op[2]
    SLICE_X48Y85         LUT2 (Prop_lut2_I1_O)        0.124     7.510 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.265     8.775    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y79         LUT3 (Prop_lut3_I2_O)        0.150     8.925 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.645     9.570    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y79         LUT6 (Prop_lut6_I1_O)        0.326     9.896 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.769    10.665    Core_cpu/U_RF/sext_ext[13]
    SLICE_X35Y78         LUT4 (Prop_lut4_I0_O)        0.124    10.789 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.771    11.560    Core_cpu/U_RF/alu_B[13]
    SLICE_X35Y82         LUT3 (Prop_lut3_I2_O)        0.124    11.684 r  Core_cpu/U_RF/C0_carry__2_i_7/O
                         net (fo=1, routed)           0.000    11.684    Core_cpu/U_ALU/Mem_DRAM_i_121_0[1]
    SLICE_X35Y82         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    12.234 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    12.234    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X35Y83         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.348 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    12.348    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X35Y84         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.462 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.462    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X35Y85         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.576 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.576    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X35Y86         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.815 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.984    13.799    Core_cpu/U_ALU/data0[30]
    SLICE_X40Y87         LUT3 (Prop_lut3_I2_O)        0.328    14.127 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.730    14.857    Core_cpu/U_RF/pc_reg[30]
    SLICE_X40Y89         LUT6 (Prop_lut6_I0_O)        0.326    15.183 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.403    16.586    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X51Y88         LUT5 (Prop_lut5_I4_O)        0.150    16.736 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.854    17.590    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X49Y89         LUT2 (Prop_lut2_I1_O)        0.354    17.944 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.756    18.700    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X51Y87         LUT2 (Prop_lut2_I0_O)        0.351    19.051 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.618    19.669    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X44Y87         LUT2 (Prop_lut2_I1_O)        0.332    20.001 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.231    22.232    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X9Y73          LUT2 (Prop_lut2_I0_O)        0.118    22.350 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.656    23.005    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X11Y78         LUT6 (Prop_lut6_I5_O)        0.326    23.331 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_0_0_i_1/O
                         net (fo=128, routed)         4.931    28.263    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WE
    SLICE_X56Y3          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_A/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.985    38.494    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.594 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    39.232    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.323 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.453    40.776    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WCLK
    SLICE_X56Y3          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_A/CLK
                         clock pessimism             -0.144    40.632    
                         clock uncertainty           -0.180    40.452    
    SLICE_X56Y3          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.919    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_A
  -------------------------------------------------------------------
                         required time                         39.919    
                         arrival time                         -28.263    
  -------------------------------------------------------------------
                         slack                                 11.657    

Slack (MET) :             11.657ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_B/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.539ns  (logic 5.340ns (19.391%)  route 22.199ns (80.609%))
  Logic Levels:           23  (CARRY4=5 LUT2=5 LUT3=3 LUT4=1 LUT5=4 LUT6=5)
  Clock Path Skew:        -0.092ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.776ns = ( 40.776 - 40.000 ) 
    Source Clock Delay      (SCD):    0.724ns
    Clock Pessimism Removal (CPR):    -0.144ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.236    -1.762    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.638 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -0.917    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -0.821 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.545     0.724    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X51Y78         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y78         FDCE (Prop_fdce_C_Q)         0.456     1.180 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.668     2.848    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     2.972 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.405    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2_n_0
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     3.529 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1/O
                         net (fo=1, routed)           0.505     4.034    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1_n_0
    SLICE_X58Y84         LUT5 (Prop_lut5_I2_O)        0.124     4.158 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0/O
                         net (fo=7, routed)           1.689     5.847    Core_cpu/U_PC/spo[12]
    SLICE_X47Y89         LUT5 (Prop_lut5_I0_O)        0.124     5.971 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.433     6.404    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X47Y89         LUT5 (Prop_lut5_I4_O)        0.124     6.528 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.858     7.386    Core_cpu/U_PC/sext_op[2]
    SLICE_X48Y85         LUT2 (Prop_lut2_I1_O)        0.124     7.510 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.265     8.775    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y79         LUT3 (Prop_lut3_I2_O)        0.150     8.925 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.645     9.570    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y79         LUT6 (Prop_lut6_I1_O)        0.326     9.896 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.769    10.665    Core_cpu/U_RF/sext_ext[13]
    SLICE_X35Y78         LUT4 (Prop_lut4_I0_O)        0.124    10.789 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.771    11.560    Core_cpu/U_RF/alu_B[13]
    SLICE_X35Y82         LUT3 (Prop_lut3_I2_O)        0.124    11.684 r  Core_cpu/U_RF/C0_carry__2_i_7/O
                         net (fo=1, routed)           0.000    11.684    Core_cpu/U_ALU/Mem_DRAM_i_121_0[1]
    SLICE_X35Y82         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    12.234 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    12.234    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X35Y83         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.348 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    12.348    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X35Y84         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.462 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.462    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X35Y85         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.576 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.576    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X35Y86         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.815 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.984    13.799    Core_cpu/U_ALU/data0[30]
    SLICE_X40Y87         LUT3 (Prop_lut3_I2_O)        0.328    14.127 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.730    14.857    Core_cpu/U_RF/pc_reg[30]
    SLICE_X40Y89         LUT6 (Prop_lut6_I0_O)        0.326    15.183 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.403    16.586    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X51Y88         LUT5 (Prop_lut5_I4_O)        0.150    16.736 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.854    17.590    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X49Y89         LUT2 (Prop_lut2_I1_O)        0.354    17.944 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.756    18.700    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X51Y87         LUT2 (Prop_lut2_I0_O)        0.351    19.051 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.618    19.669    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X44Y87         LUT2 (Prop_lut2_I1_O)        0.332    20.001 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.231    22.232    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X9Y73          LUT2 (Prop_lut2_I0_O)        0.118    22.350 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.656    23.005    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X11Y78         LUT6 (Prop_lut6_I5_O)        0.326    23.331 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_0_0_i_1/O
                         net (fo=128, routed)         4.931    28.263    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WE
    SLICE_X56Y3          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_B/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.985    38.494    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.594 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    39.232    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.323 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.453    40.776    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WCLK
    SLICE_X56Y3          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_B/CLK
                         clock pessimism             -0.144    40.632    
                         clock uncertainty           -0.180    40.452    
    SLICE_X56Y3          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.919    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_B
  -------------------------------------------------------------------
                         required time                         39.919    
                         arrival time                         -28.263    
  -------------------------------------------------------------------
                         slack                                 11.657    

Slack (MET) :             11.657ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_C/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.539ns  (logic 5.340ns (19.391%)  route 22.199ns (80.609%))
  Logic Levels:           23  (CARRY4=5 LUT2=5 LUT3=3 LUT4=1 LUT5=4 LUT6=5)
  Clock Path Skew:        -0.092ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.776ns = ( 40.776 - 40.000 ) 
    Source Clock Delay      (SCD):    0.724ns
    Clock Pessimism Removal (CPR):    -0.144ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.236    -1.762    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.638 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -0.917    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -0.821 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.545     0.724    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X51Y78         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y78         FDCE (Prop_fdce_C_Q)         0.456     1.180 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.668     2.848    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     2.972 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.405    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2_n_0
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     3.529 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1/O
                         net (fo=1, routed)           0.505     4.034    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1_n_0
    SLICE_X58Y84         LUT5 (Prop_lut5_I2_O)        0.124     4.158 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0/O
                         net (fo=7, routed)           1.689     5.847    Core_cpu/U_PC/spo[12]
    SLICE_X47Y89         LUT5 (Prop_lut5_I0_O)        0.124     5.971 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.433     6.404    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X47Y89         LUT5 (Prop_lut5_I4_O)        0.124     6.528 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.858     7.386    Core_cpu/U_PC/sext_op[2]
    SLICE_X48Y85         LUT2 (Prop_lut2_I1_O)        0.124     7.510 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.265     8.775    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y79         LUT3 (Prop_lut3_I2_O)        0.150     8.925 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.645     9.570    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y79         LUT6 (Prop_lut6_I1_O)        0.326     9.896 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.769    10.665    Core_cpu/U_RF/sext_ext[13]
    SLICE_X35Y78         LUT4 (Prop_lut4_I0_O)        0.124    10.789 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.771    11.560    Core_cpu/U_RF/alu_B[13]
    SLICE_X35Y82         LUT3 (Prop_lut3_I2_O)        0.124    11.684 r  Core_cpu/U_RF/C0_carry__2_i_7/O
                         net (fo=1, routed)           0.000    11.684    Core_cpu/U_ALU/Mem_DRAM_i_121_0[1]
    SLICE_X35Y82         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    12.234 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    12.234    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X35Y83         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.348 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    12.348    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X35Y84         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.462 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.462    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X35Y85         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.576 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.576    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X35Y86         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.815 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.984    13.799    Core_cpu/U_ALU/data0[30]
    SLICE_X40Y87         LUT3 (Prop_lut3_I2_O)        0.328    14.127 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.730    14.857    Core_cpu/U_RF/pc_reg[30]
    SLICE_X40Y89         LUT6 (Prop_lut6_I0_O)        0.326    15.183 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.403    16.586    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X51Y88         LUT5 (Prop_lut5_I4_O)        0.150    16.736 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.854    17.590    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X49Y89         LUT2 (Prop_lut2_I1_O)        0.354    17.944 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.756    18.700    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X51Y87         LUT2 (Prop_lut2_I0_O)        0.351    19.051 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.618    19.669    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X44Y87         LUT2 (Prop_lut2_I1_O)        0.332    20.001 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.231    22.232    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X9Y73          LUT2 (Prop_lut2_I0_O)        0.118    22.350 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.656    23.005    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X11Y78         LUT6 (Prop_lut6_I5_O)        0.326    23.331 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_0_0_i_1/O
                         net (fo=128, routed)         4.931    28.263    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WE
    SLICE_X56Y3          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_C/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.985    38.494    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.594 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    39.232    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.323 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.453    40.776    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WCLK
    SLICE_X56Y3          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_C/CLK
                         clock pessimism             -0.144    40.632    
                         clock uncertainty           -0.180    40.452    
    SLICE_X56Y3          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.919    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_C
  -------------------------------------------------------------------
                         required time                         39.919    
                         arrival time                         -28.263    
  -------------------------------------------------------------------
                         slack                                 11.657    

Slack (MET) :             11.657ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_D/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.539ns  (logic 5.340ns (19.391%)  route 22.199ns (80.609%))
  Logic Levels:           23  (CARRY4=5 LUT2=5 LUT3=3 LUT4=1 LUT5=4 LUT6=5)
  Clock Path Skew:        -0.092ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.776ns = ( 40.776 - 40.000 ) 
    Source Clock Delay      (SCD):    0.724ns
    Clock Pessimism Removal (CPR):    -0.144ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.236    -1.762    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.638 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -0.917    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -0.821 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.545     0.724    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X51Y78         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y78         FDCE (Prop_fdce_C_Q)         0.456     1.180 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.668     2.848    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     2.972 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.405    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2_n_0
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     3.529 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1/O
                         net (fo=1, routed)           0.505     4.034    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1_n_0
    SLICE_X58Y84         LUT5 (Prop_lut5_I2_O)        0.124     4.158 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0/O
                         net (fo=7, routed)           1.689     5.847    Core_cpu/U_PC/spo[12]
    SLICE_X47Y89         LUT5 (Prop_lut5_I0_O)        0.124     5.971 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.433     6.404    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X47Y89         LUT5 (Prop_lut5_I4_O)        0.124     6.528 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.858     7.386    Core_cpu/U_PC/sext_op[2]
    SLICE_X48Y85         LUT2 (Prop_lut2_I1_O)        0.124     7.510 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.265     8.775    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y79         LUT3 (Prop_lut3_I2_O)        0.150     8.925 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.645     9.570    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y79         LUT6 (Prop_lut6_I1_O)        0.326     9.896 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.769    10.665    Core_cpu/U_RF/sext_ext[13]
    SLICE_X35Y78         LUT4 (Prop_lut4_I0_O)        0.124    10.789 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.771    11.560    Core_cpu/U_RF/alu_B[13]
    SLICE_X35Y82         LUT3 (Prop_lut3_I2_O)        0.124    11.684 r  Core_cpu/U_RF/C0_carry__2_i_7/O
                         net (fo=1, routed)           0.000    11.684    Core_cpu/U_ALU/Mem_DRAM_i_121_0[1]
    SLICE_X35Y82         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    12.234 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    12.234    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X35Y83         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.348 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    12.348    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X35Y84         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.462 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.462    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X35Y85         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.576 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.576    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X35Y86         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.815 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.984    13.799    Core_cpu/U_ALU/data0[30]
    SLICE_X40Y87         LUT3 (Prop_lut3_I2_O)        0.328    14.127 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.730    14.857    Core_cpu/U_RF/pc_reg[30]
    SLICE_X40Y89         LUT6 (Prop_lut6_I0_O)        0.326    15.183 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.403    16.586    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X51Y88         LUT5 (Prop_lut5_I4_O)        0.150    16.736 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.854    17.590    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X49Y89         LUT2 (Prop_lut2_I1_O)        0.354    17.944 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.756    18.700    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X51Y87         LUT2 (Prop_lut2_I0_O)        0.351    19.051 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.618    19.669    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X44Y87         LUT2 (Prop_lut2_I1_O)        0.332    20.001 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.231    22.232    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X9Y73          LUT2 (Prop_lut2_I0_O)        0.118    22.350 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.656    23.005    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X11Y78         LUT6 (Prop_lut6_I5_O)        0.326    23.331 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_0_0_i_1/O
                         net (fo=128, routed)         4.931    28.263    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WE
    SLICE_X56Y3          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_D/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.985    38.494    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.594 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    39.232    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.323 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.453    40.776    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/WCLK
    SLICE_X56Y3          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_D/CLK
                         clock pessimism             -0.144    40.632    
                         clock uncertainty           -0.180    40.452    
    SLICE_X56Y3          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.919    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5632_5887_12_12/RAMS64E_D
  -------------------------------------------------------------------
                         required time                         39.919    
                         arrival time                         -28.263    
  -------------------------------------------------------------------
                         slack                                 11.657    

Slack (MET) :             11.799ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_A/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.395ns  (logic 5.340ns (19.492%)  route 22.055ns (80.508%))
  Logic Levels:           23  (CARRY4=5 LUT2=5 LUT3=3 LUT4=1 LUT5=4 LUT6=5)
  Clock Path Skew:        -0.094ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.774ns = ( 40.774 - 40.000 ) 
    Source Clock Delay      (SCD):    0.724ns
    Clock Pessimism Removal (CPR):    -0.144ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.236    -1.762    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.638 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -0.917    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -0.821 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.545     0.724    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X51Y78         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y78         FDCE (Prop_fdce_C_Q)         0.456     1.180 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.668     2.848    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     2.972 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.405    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2_n_0
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     3.529 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1/O
                         net (fo=1, routed)           0.505     4.034    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1_n_0
    SLICE_X58Y84         LUT5 (Prop_lut5_I2_O)        0.124     4.158 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0/O
                         net (fo=7, routed)           1.689     5.847    Core_cpu/U_PC/spo[12]
    SLICE_X47Y89         LUT5 (Prop_lut5_I0_O)        0.124     5.971 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.433     6.404    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X47Y89         LUT5 (Prop_lut5_I4_O)        0.124     6.528 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.858     7.386    Core_cpu/U_PC/sext_op[2]
    SLICE_X48Y85         LUT2 (Prop_lut2_I1_O)        0.124     7.510 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.265     8.775    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y79         LUT3 (Prop_lut3_I2_O)        0.150     8.925 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.645     9.570    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y79         LUT6 (Prop_lut6_I1_O)        0.326     9.896 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.769    10.665    Core_cpu/U_RF/sext_ext[13]
    SLICE_X35Y78         LUT4 (Prop_lut4_I0_O)        0.124    10.789 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.771    11.560    Core_cpu/U_RF/alu_B[13]
    SLICE_X35Y82         LUT3 (Prop_lut3_I2_O)        0.124    11.684 r  Core_cpu/U_RF/C0_carry__2_i_7/O
                         net (fo=1, routed)           0.000    11.684    Core_cpu/U_ALU/Mem_DRAM_i_121_0[1]
    SLICE_X35Y82         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    12.234 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    12.234    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X35Y83         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.348 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    12.348    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X35Y84         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.462 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.462    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X35Y85         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.576 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.576    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X35Y86         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.815 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.984    13.799    Core_cpu/U_ALU/data0[30]
    SLICE_X40Y87         LUT3 (Prop_lut3_I2_O)        0.328    14.127 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.730    14.857    Core_cpu/U_RF/pc_reg[30]
    SLICE_X40Y89         LUT6 (Prop_lut6_I0_O)        0.326    15.183 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.403    16.586    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X51Y88         LUT5 (Prop_lut5_I4_O)        0.150    16.736 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.854    17.590    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X49Y89         LUT2 (Prop_lut2_I1_O)        0.354    17.944 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.756    18.700    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X51Y87         LUT2 (Prop_lut2_I0_O)        0.351    19.051 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.618    19.669    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X44Y87         LUT2 (Prop_lut2_I1_O)        0.332    20.001 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.231    22.232    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X9Y73          LUT2 (Prop_lut2_I0_O)        0.118    22.350 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.659    23.008    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X11Y78         LUT6 (Prop_lut6_I5_O)        0.326    23.334 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_1/O
                         net (fo=128, routed)         4.785    28.119    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/WE
    SLICE_X54Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_A/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.985    38.494    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.594 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    39.232    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.323 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.451    40.774    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/WCLK
    SLICE_X54Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_A/CLK
                         clock pessimism             -0.144    40.630    
                         clock uncertainty           -0.180    40.450    
    SLICE_X54Y8          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.917    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_A
  -------------------------------------------------------------------
                         required time                         39.917    
                         arrival time                         -28.119    
  -------------------------------------------------------------------
                         slack                                 11.799    

Slack (MET) :             11.799ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_B/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.395ns  (logic 5.340ns (19.492%)  route 22.055ns (80.508%))
  Logic Levels:           23  (CARRY4=5 LUT2=5 LUT3=3 LUT4=1 LUT5=4 LUT6=5)
  Clock Path Skew:        -0.094ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.774ns = ( 40.774 - 40.000 ) 
    Source Clock Delay      (SCD):    0.724ns
    Clock Pessimism Removal (CPR):    -0.144ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.236    -1.762    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.638 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -0.917    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -0.821 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.545     0.724    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X51Y78         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y78         FDCE (Prop_fdce_C_Q)         0.456     1.180 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.668     2.848    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     2.972 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.405    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2_n_0
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     3.529 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1/O
                         net (fo=1, routed)           0.505     4.034    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1_n_0
    SLICE_X58Y84         LUT5 (Prop_lut5_I2_O)        0.124     4.158 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0/O
                         net (fo=7, routed)           1.689     5.847    Core_cpu/U_PC/spo[12]
    SLICE_X47Y89         LUT5 (Prop_lut5_I0_O)        0.124     5.971 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.433     6.404    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X47Y89         LUT5 (Prop_lut5_I4_O)        0.124     6.528 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.858     7.386    Core_cpu/U_PC/sext_op[2]
    SLICE_X48Y85         LUT2 (Prop_lut2_I1_O)        0.124     7.510 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.265     8.775    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y79         LUT3 (Prop_lut3_I2_O)        0.150     8.925 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.645     9.570    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y79         LUT6 (Prop_lut6_I1_O)        0.326     9.896 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.769    10.665    Core_cpu/U_RF/sext_ext[13]
    SLICE_X35Y78         LUT4 (Prop_lut4_I0_O)        0.124    10.789 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.771    11.560    Core_cpu/U_RF/alu_B[13]
    SLICE_X35Y82         LUT3 (Prop_lut3_I2_O)        0.124    11.684 r  Core_cpu/U_RF/C0_carry__2_i_7/O
                         net (fo=1, routed)           0.000    11.684    Core_cpu/U_ALU/Mem_DRAM_i_121_0[1]
    SLICE_X35Y82         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    12.234 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    12.234    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X35Y83         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.348 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    12.348    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X35Y84         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.462 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.462    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X35Y85         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.576 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.576    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X35Y86         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.815 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.984    13.799    Core_cpu/U_ALU/data0[30]
    SLICE_X40Y87         LUT3 (Prop_lut3_I2_O)        0.328    14.127 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.730    14.857    Core_cpu/U_RF/pc_reg[30]
    SLICE_X40Y89         LUT6 (Prop_lut6_I0_O)        0.326    15.183 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.403    16.586    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X51Y88         LUT5 (Prop_lut5_I4_O)        0.150    16.736 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.854    17.590    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X49Y89         LUT2 (Prop_lut2_I1_O)        0.354    17.944 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.756    18.700    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X51Y87         LUT2 (Prop_lut2_I0_O)        0.351    19.051 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.618    19.669    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X44Y87         LUT2 (Prop_lut2_I1_O)        0.332    20.001 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.231    22.232    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X9Y73          LUT2 (Prop_lut2_I0_O)        0.118    22.350 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.659    23.008    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X11Y78         LUT6 (Prop_lut6_I5_O)        0.326    23.334 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_1/O
                         net (fo=128, routed)         4.785    28.119    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/WE
    SLICE_X54Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_B/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.985    38.494    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.594 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    39.232    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.323 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.451    40.774    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/WCLK
    SLICE_X54Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_B/CLK
                         clock pessimism             -0.144    40.630    
                         clock uncertainty           -0.180    40.450    
    SLICE_X54Y8          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.917    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_B
  -------------------------------------------------------------------
                         required time                         39.917    
                         arrival time                         -28.119    
  -------------------------------------------------------------------
                         slack                                 11.799    

Slack (MET) :             11.799ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_C/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.395ns  (logic 5.340ns (19.492%)  route 22.055ns (80.508%))
  Logic Levels:           23  (CARRY4=5 LUT2=5 LUT3=3 LUT4=1 LUT5=4 LUT6=5)
  Clock Path Skew:        -0.094ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.774ns = ( 40.774 - 40.000 ) 
    Source Clock Delay      (SCD):    0.724ns
    Clock Pessimism Removal (CPR):    -0.144ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.236    -1.762    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.638 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -0.917    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -0.821 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.545     0.724    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X51Y78         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y78         FDCE (Prop_fdce_C_Q)         0.456     1.180 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.668     2.848    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     2.972 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.405    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2_n_0
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     3.529 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1/O
                         net (fo=1, routed)           0.505     4.034    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1_n_0
    SLICE_X58Y84         LUT5 (Prop_lut5_I2_O)        0.124     4.158 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0/O
                         net (fo=7, routed)           1.689     5.847    Core_cpu/U_PC/spo[12]
    SLICE_X47Y89         LUT5 (Prop_lut5_I0_O)        0.124     5.971 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.433     6.404    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X47Y89         LUT5 (Prop_lut5_I4_O)        0.124     6.528 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.858     7.386    Core_cpu/U_PC/sext_op[2]
    SLICE_X48Y85         LUT2 (Prop_lut2_I1_O)        0.124     7.510 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.265     8.775    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y79         LUT3 (Prop_lut3_I2_O)        0.150     8.925 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.645     9.570    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y79         LUT6 (Prop_lut6_I1_O)        0.326     9.896 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.769    10.665    Core_cpu/U_RF/sext_ext[13]
    SLICE_X35Y78         LUT4 (Prop_lut4_I0_O)        0.124    10.789 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.771    11.560    Core_cpu/U_RF/alu_B[13]
    SLICE_X35Y82         LUT3 (Prop_lut3_I2_O)        0.124    11.684 r  Core_cpu/U_RF/C0_carry__2_i_7/O
                         net (fo=1, routed)           0.000    11.684    Core_cpu/U_ALU/Mem_DRAM_i_121_0[1]
    SLICE_X35Y82         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    12.234 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    12.234    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X35Y83         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.348 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    12.348    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X35Y84         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.462 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.462    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X35Y85         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.576 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.576    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X35Y86         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.815 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.984    13.799    Core_cpu/U_ALU/data0[30]
    SLICE_X40Y87         LUT3 (Prop_lut3_I2_O)        0.328    14.127 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.730    14.857    Core_cpu/U_RF/pc_reg[30]
    SLICE_X40Y89         LUT6 (Prop_lut6_I0_O)        0.326    15.183 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.403    16.586    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X51Y88         LUT5 (Prop_lut5_I4_O)        0.150    16.736 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.854    17.590    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X49Y89         LUT2 (Prop_lut2_I1_O)        0.354    17.944 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.756    18.700    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X51Y87         LUT2 (Prop_lut2_I0_O)        0.351    19.051 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.618    19.669    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X44Y87         LUT2 (Prop_lut2_I1_O)        0.332    20.001 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.231    22.232    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X9Y73          LUT2 (Prop_lut2_I0_O)        0.118    22.350 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.659    23.008    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X11Y78         LUT6 (Prop_lut6_I5_O)        0.326    23.334 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_1/O
                         net (fo=128, routed)         4.785    28.119    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/WE
    SLICE_X54Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_C/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.985    38.494    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.594 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    39.232    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.323 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.451    40.774    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/WCLK
    SLICE_X54Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_C/CLK
                         clock pessimism             -0.144    40.630    
                         clock uncertainty           -0.180    40.450    
    SLICE_X54Y8          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.917    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_C
  -------------------------------------------------------------------
                         required time                         39.917    
                         arrival time                         -28.119    
  -------------------------------------------------------------------
                         slack                                 11.799    

Slack (MET) :             11.799ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_D/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.395ns  (logic 5.340ns (19.492%)  route 22.055ns (80.508%))
  Logic Levels:           23  (CARRY4=5 LUT2=5 LUT3=3 LUT4=1 LUT5=4 LUT6=5)
  Clock Path Skew:        -0.094ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.774ns = ( 40.774 - 40.000 ) 
    Source Clock Delay      (SCD):    0.724ns
    Clock Pessimism Removal (CPR):    -0.144ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.236    -1.762    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.638 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -0.917    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -0.821 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.545     0.724    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X51Y78         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y78         FDCE (Prop_fdce_C_Q)         0.456     1.180 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.668     2.848    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     2.972 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.405    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2_n_0
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     3.529 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1/O
                         net (fo=1, routed)           0.505     4.034    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1_n_0
    SLICE_X58Y84         LUT5 (Prop_lut5_I2_O)        0.124     4.158 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0/O
                         net (fo=7, routed)           1.689     5.847    Core_cpu/U_PC/spo[12]
    SLICE_X47Y89         LUT5 (Prop_lut5_I0_O)        0.124     5.971 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.433     6.404    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X47Y89         LUT5 (Prop_lut5_I4_O)        0.124     6.528 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.858     7.386    Core_cpu/U_PC/sext_op[2]
    SLICE_X48Y85         LUT2 (Prop_lut2_I1_O)        0.124     7.510 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.265     8.775    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y79         LUT3 (Prop_lut3_I2_O)        0.150     8.925 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.645     9.570    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y79         LUT6 (Prop_lut6_I1_O)        0.326     9.896 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.769    10.665    Core_cpu/U_RF/sext_ext[13]
    SLICE_X35Y78         LUT4 (Prop_lut4_I0_O)        0.124    10.789 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.771    11.560    Core_cpu/U_RF/alu_B[13]
    SLICE_X35Y82         LUT3 (Prop_lut3_I2_O)        0.124    11.684 r  Core_cpu/U_RF/C0_carry__2_i_7/O
                         net (fo=1, routed)           0.000    11.684    Core_cpu/U_ALU/Mem_DRAM_i_121_0[1]
    SLICE_X35Y82         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    12.234 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    12.234    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X35Y83         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.348 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    12.348    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X35Y84         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.462 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.462    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X35Y85         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.576 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.576    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X35Y86         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.815 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.984    13.799    Core_cpu/U_ALU/data0[30]
    SLICE_X40Y87         LUT3 (Prop_lut3_I2_O)        0.328    14.127 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.730    14.857    Core_cpu/U_RF/pc_reg[30]
    SLICE_X40Y89         LUT6 (Prop_lut6_I0_O)        0.326    15.183 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.403    16.586    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X51Y88         LUT5 (Prop_lut5_I4_O)        0.150    16.736 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.854    17.590    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X49Y89         LUT2 (Prop_lut2_I1_O)        0.354    17.944 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.756    18.700    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X51Y87         LUT2 (Prop_lut2_I0_O)        0.351    19.051 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.618    19.669    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X44Y87         LUT2 (Prop_lut2_I1_O)        0.332    20.001 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.231    22.232    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X9Y73          LUT2 (Prop_lut2_I0_O)        0.118    22.350 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.659    23.008    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X11Y78         LUT6 (Prop_lut6_I5_O)        0.326    23.334 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_1/O
                         net (fo=128, routed)         4.785    28.119    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/WE
    SLICE_X54Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_D/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.985    38.494    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.594 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    39.232    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.323 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.451    40.774    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/WCLK
    SLICE_X54Y8          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_D/CLK
                         clock pessimism             -0.144    40.630    
                         clock uncertainty           -0.180    40.450    
    SLICE_X54Y8          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.917    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_12_12/RAMS64E_D
  -------------------------------------------------------------------
                         required time                         39.917    
                         arrival time                         -28.119    
  -------------------------------------------------------------------
                         slack                                 11.799    

Slack (MET) :             11.919ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/RAMS64E_A/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.270ns  (logic 5.340ns (19.582%)  route 21.930ns (80.418%))
  Logic Levels:           23  (CARRY4=5 LUT2=5 LUT3=3 LUT4=1 LUT5=4 LUT6=5)
  Clock Path Skew:        -0.099ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.769ns = ( 40.769 - 40.000 ) 
    Source Clock Delay      (SCD):    0.724ns
    Clock Pessimism Removal (CPR):    -0.144ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.236    -1.762    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.638 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -0.917    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -0.821 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.545     0.724    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X51Y78         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y78         FDCE (Prop_fdce_C_Q)         0.456     1.180 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.668     2.848    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     2.972 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.405    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2_n_0
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     3.529 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1/O
                         net (fo=1, routed)           0.505     4.034    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1_n_0
    SLICE_X58Y84         LUT5 (Prop_lut5_I2_O)        0.124     4.158 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0/O
                         net (fo=7, routed)           1.689     5.847    Core_cpu/U_PC/spo[12]
    SLICE_X47Y89         LUT5 (Prop_lut5_I0_O)        0.124     5.971 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.433     6.404    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X47Y89         LUT5 (Prop_lut5_I4_O)        0.124     6.528 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.858     7.386    Core_cpu/U_PC/sext_op[2]
    SLICE_X48Y85         LUT2 (Prop_lut2_I1_O)        0.124     7.510 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.265     8.775    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y79         LUT3 (Prop_lut3_I2_O)        0.150     8.925 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.645     9.570    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y79         LUT6 (Prop_lut6_I1_O)        0.326     9.896 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.769    10.665    Core_cpu/U_RF/sext_ext[13]
    SLICE_X35Y78         LUT4 (Prop_lut4_I0_O)        0.124    10.789 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.771    11.560    Core_cpu/U_RF/alu_B[13]
    SLICE_X35Y82         LUT3 (Prop_lut3_I2_O)        0.124    11.684 r  Core_cpu/U_RF/C0_carry__2_i_7/O
                         net (fo=1, routed)           0.000    11.684    Core_cpu/U_ALU/Mem_DRAM_i_121_0[1]
    SLICE_X35Y82         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    12.234 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    12.234    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X35Y83         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.348 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    12.348    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X35Y84         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.462 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.462    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X35Y85         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.576 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.576    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X35Y86         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.815 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.984    13.799    Core_cpu/U_ALU/data0[30]
    SLICE_X40Y87         LUT3 (Prop_lut3_I2_O)        0.328    14.127 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.730    14.857    Core_cpu/U_RF/pc_reg[30]
    SLICE_X40Y89         LUT6 (Prop_lut6_I0_O)        0.326    15.183 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.403    16.586    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X51Y88         LUT5 (Prop_lut5_I4_O)        0.150    16.736 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.854    17.590    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X49Y89         LUT2 (Prop_lut2_I1_O)        0.354    17.944 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.756    18.700    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X51Y87         LUT2 (Prop_lut2_I0_O)        0.351    19.051 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.618    19.669    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X44Y87         LUT2 (Prop_lut2_I1_O)        0.332    20.001 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.231    22.232    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X9Y73          LUT2 (Prop_lut2_I0_O)        0.118    22.350 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.775    23.125    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X11Y78         LUT6 (Prop_lut6_I5_O)        0.326    23.451 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_0_0_i_1/O
                         net (fo=128, routed)         4.542    27.993    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/WE
    SLICE_X46Y9          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/RAMS64E_A/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.985    38.494    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.594 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    39.232    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.323 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.446    40.769    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/WCLK
    SLICE_X46Y9          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/RAMS64E_A/CLK
                         clock pessimism             -0.144    40.625    
                         clock uncertainty           -0.180    40.445    
    SLICE_X46Y9          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.912    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/RAMS64E_A
  -------------------------------------------------------------------
                         required time                         39.912    
                         arrival time                         -27.993    
  -------------------------------------------------------------------
                         slack                                 11.919    

Slack (MET) :             11.919ns  (required time - arrival time)
  Source:                 Core_cpu/U_PC/pc_reg[6]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/RAMS64E_B/WE
                            (rising edge-triggered cell RAMS64E clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            40.000ns  (clk_out1_cpuclk rise@40.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        27.270ns  (logic 5.340ns (19.582%)  route 21.930ns (80.418%))
  Logic Levels:           23  (CARRY4=5 LUT2=5 LUT3=3 LUT4=1 LUT5=4 LUT6=5)
  Clock Path Skew:        -0.099ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    0.769ns = ( 40.769 - 40.000 ) 
    Source Clock Delay      (SCD):    0.724ns
    Clock Pessimism Removal (CPR):    -0.144ns
  Clock Uncertainty:      0.180ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.352ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.478     1.478 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.253     2.731    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -8.486    -5.754 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.660    -4.094    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.096    -3.998 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           2.236    -1.762    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.124    -1.638 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.720    -0.917    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.096    -0.821 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.545     0.724    Core_cpu/U_PC/cpu_clk_BUFG
    SLICE_X51Y78         FDCE                                         r  Core_cpu/U_PC/pc_reg[6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y78         FDCE (Prop_fdce_C_Q)         0.456     1.180 r  Core_cpu/U_PC/pc_reg[6]/Q
                         net (fo=55, routed)          1.668     2.848    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/a[4]
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     2.972 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2/O
                         net (fo=1, routed)           0.433     3.405    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_2_n_0
    SLICE_X59Y84         LUT6 (Prop_lut6_I1_O)        0.124     3.529 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1/O
                         net (fo=1, routed)           0.505     4.034    Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0_i_1_n_0
    SLICE_X58Y84         LUT5 (Prop_lut5_I2_O)        0.124     4.158 r  Mem_IROM/U0/synth_options.dist_mem_inst/gen_rom.rom_inst/spo[13]_INST_0/O
                         net (fo=7, routed)           1.689     5.847    Core_cpu/U_PC/spo[12]
    SLICE_X47Y89         LUT5 (Prop_lut5_I0_O)        0.124     5.971 f  Core_cpu/U_PC/npc0_carry_i_9/O
                         net (fo=1, routed)           0.433     6.404    Core_cpu/U_PC/npc0_carry_i_9_n_0
    SLICE_X47Y89         LUT5 (Prop_lut5_I4_O)        0.124     6.528 f  Core_cpu/U_PC/npc0_carry_i_5/O
                         net (fo=41, routed)          0.858     7.386    Core_cpu/U_PC/sext_op[2]
    SLICE_X48Y85         LUT2 (Prop_lut2_I1_O)        0.124     7.510 r  Core_cpu/U_PC/npc0_carry__0_i_7/O
                         net (fo=32, routed)          1.265     8.775    Core_cpu/U_RF/f0_carry_i_2_0
    SLICE_X43Y79         LUT3 (Prop_lut3_I2_O)        0.150     8.925 r  Core_cpu/U_RF/registers_reg_r1_0_31_12_17_i_41/O
                         net (fo=8, routed)           0.645     9.570    Core_cpu/U_RF/npc0_carry__0_i_7
    SLICE_X45Y79         LUT6 (Prop_lut6_I1_O)        0.326     9.896 r  Core_cpu/U_RF/C2_carry__0_i_21/O
                         net (fo=3, routed)           0.769    10.665    Core_cpu/U_RF/sext_ext[13]
    SLICE_X35Y78         LUT4 (Prop_lut4_I0_O)        0.124    10.789 r  Core_cpu/U_RF/C2_carry__0_i_12/O
                         net (fo=7, routed)           0.771    11.560    Core_cpu/U_RF/alu_B[13]
    SLICE_X35Y82         LUT3 (Prop_lut3_I2_O)        0.124    11.684 r  Core_cpu/U_RF/C0_carry__2_i_7/O
                         net (fo=1, routed)           0.000    11.684    Core_cpu/U_ALU/Mem_DRAM_i_121_0[1]
    SLICE_X35Y82         CARRY4 (Prop_carry4_S[1]_CO[3])
                                                      0.550    12.234 r  Core_cpu/U_ALU/C0_carry__2/CO[3]
                         net (fo=1, routed)           0.000    12.234    Core_cpu/U_ALU/C0_carry__2_n_0
    SLICE_X35Y83         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.348 r  Core_cpu/U_ALU/C0_carry__3/CO[3]
                         net (fo=1, routed)           0.000    12.348    Core_cpu/U_ALU/C0_carry__3_n_0
    SLICE_X35Y84         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.462 r  Core_cpu/U_ALU/C0_carry__4/CO[3]
                         net (fo=1, routed)           0.000    12.462    Core_cpu/U_ALU/C0_carry__4_n_0
    SLICE_X35Y85         CARRY4 (Prop_carry4_CI_CO[3])
                                                      0.114    12.576 r  Core_cpu/U_ALU/C0_carry__5/CO[3]
                         net (fo=1, routed)           0.000    12.576    Core_cpu/U_ALU/C0_carry__5_n_0
    SLICE_X35Y86         CARRY4 (Prop_carry4_CI_O[2])
                                                      0.239    12.815 f  Core_cpu/U_ALU/C0_carry__6/O[2]
                         net (fo=2, routed)           0.984    13.799    Core_cpu/U_ALU/data0[30]
    SLICE_X40Y87         LUT3 (Prop_lut3_I2_O)        0.328    14.127 f  Core_cpu/U_ALU/pc[30]_i_3/O
                         net (fo=1, routed)           0.730    14.857    Core_cpu/U_RF/pc_reg[30]
    SLICE_X40Y89         LUT6 (Prop_lut6_I0_O)        0.326    15.183 f  Core_cpu/U_RF/pc[30]_i_2/O
                         net (fo=8, routed)           1.403    16.586    Core_cpu/U_RF/Bus_addr__0[30]
    SLICE_X51Y88         LUT5 (Prop_lut5_I4_O)        0.150    16.736 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59/O
                         net (fo=4, routed)           0.854    17.590    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_59_n_0
    SLICE_X49Y89         LUT2 (Prop_lut2_I1_O)        0.354    17.944 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32/O
                         net (fo=2, routed)           0.756    18.700    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_32_n_0
    SLICE_X51Y87         LUT2 (Prop_lut2_I0_O)        0.351    19.051 r  Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_10/O
                         net (fo=46, routed)          0.618    19.669    Core_cpu/U_RF/registers_reg_r1_0_31_0_5_i_33_0
    SLICE_X44Y87         LUT2 (Prop_lut2_I1_O)        0.332    20.001 r  Core_cpu/U_RF/Mem_DRAM_i_47/O
                         net (fo=55, routed)          2.231    22.232    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/we
    SLICE_X9Y73          LUT2 (Prop_lut2_I0_O)        0.118    22.350 f  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2/O
                         net (fo=4, routed)           0.775    23.125    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_5376_5631_0_0_i_2_n_0
    SLICE_X11Y78         LUT6 (Prop_lut6_I5_O)        0.326    23.451 r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_0_0_i_1/O
                         net (fo=128, routed)         4.542    27.993    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/WE
    SLICE_X46Y9          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/RAMS64E_B/WE
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                     40.000    40.000 r  
    P17                                               0.000    40.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000    40.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         1.408    41.408 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           1.181    42.589    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -7.753    34.835 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           1.582    36.417    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.091    36.508 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.985    38.494    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.100    38.594 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.638    39.232    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.091    39.323 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        1.446    40.769    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/WCLK
    SLICE_X46Y9          RAMS64E                                      r  Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/RAMS64E_B/CLK
                         clock pessimism             -0.144    40.625    
                         clock uncertainty           -0.180    40.445    
    SLICE_X46Y9          RAMS64E (Setup_rams64e_CLK_WE)
                                                     -0.533    39.912    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_6400_6655_7_7/RAMS64E_B
  -------------------------------------------------------------------
                         required time                         39.912    
                         arrival time                         -27.993    
  -------------------------------------------------------------------
                         slack                                 11.919    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.200ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[0].debounce_cnt_reg[0][18]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.320ns  (logic 0.189ns (59.024%)  route 0.131ns (40.976%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.185ns
    Source Clock Delay      (SCD):    0.586ns
    Clock Pessimism Removal (CPR):    0.586ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.796    -0.314    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.269 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.003    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     0.023 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.563     0.586    U_Button/cpu_clk_BUFG
    SLICE_X41Y45         FDCE                                         r  U_Button/button_sync2_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X41Y45         FDCE (Prop_fdce_C_Q)         0.141     0.727 r  U_Button/button_sync2_reg[0]/Q
                         net (fo=21, routed)          0.131     0.858    U_Button/button_sync2_reg_n_0_[0]
    SLICE_X40Y45         LUT4 (Prop_lut4_I0_O)        0.048     0.906 r  U_Button/debounce_gen[0].debounce_cnt[0][18]_i_1/O
                         net (fo=1, routed)           0.000     0.906    U_Button/debounce_gen[0].debounce_cnt[0][18]_i_1_n_0
    SLICE_X40Y45         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][18]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.116    -0.032    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.024 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.323    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.352 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.833     1.185    U_Button/cpu_clk_BUFG
    SLICE_X40Y45         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][18]/C
                         clock pessimism             -0.586     0.599    
    SLICE_X40Y45         FDCE (Hold_fdce_C_D)         0.107     0.706    U_Button/debounce_gen[0].debounce_cnt_reg[0][18]
  -------------------------------------------------------------------
                         required time                         -0.706    
                         arrival time                           0.906    
  -------------------------------------------------------------------
                         slack                                  0.200    

Slack (MET) :             0.202ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[0].debounce_cnt_reg[0][17]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.322ns  (logic 0.190ns (58.968%)  route 0.132ns (41.032%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.185ns
    Source Clock Delay      (SCD):    0.586ns
    Clock Pessimism Removal (CPR):    0.586ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.796    -0.314    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.269 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.003    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     0.023 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.563     0.586    U_Button/cpu_clk_BUFG
    SLICE_X41Y45         FDCE                                         r  U_Button/button_sync2_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X41Y45         FDCE (Prop_fdce_C_Q)         0.141     0.727 r  U_Button/button_sync2_reg[0]/Q
                         net (fo=21, routed)          0.132     0.859    U_Button/button_sync2_reg_n_0_[0]
    SLICE_X40Y45         LUT4 (Prop_lut4_I0_O)        0.049     0.908 r  U_Button/debounce_gen[0].debounce_cnt[0][17]_i_1/O
                         net (fo=1, routed)           0.000     0.908    U_Button/debounce_gen[0].debounce_cnt[0][17]_i_1_n_0
    SLICE_X40Y45         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][17]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.116    -0.032    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.024 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.323    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.352 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.833     1.185    U_Button/cpu_clk_BUFG
    SLICE_X40Y45         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][17]/C
                         clock pessimism             -0.586     0.599    
    SLICE_X40Y45         FDCE (Hold_fdce_C_D)         0.107     0.706    U_Button/debounce_gen[0].debounce_cnt_reg[0][17]
  -------------------------------------------------------------------
                         required time                         -0.706    
                         arrival time                           0.908    
  -------------------------------------------------------------------
                         slack                                  0.202    

Slack (MET) :             0.213ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[0].debounce_cnt_reg[0][15]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.318ns  (logic 0.186ns (58.452%)  route 0.132ns (41.548%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.185ns
    Source Clock Delay      (SCD):    0.586ns
    Clock Pessimism Removal (CPR):    0.586ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.796    -0.314    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.269 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.003    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     0.023 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.563     0.586    U_Button/cpu_clk_BUFG
    SLICE_X41Y45         FDCE                                         r  U_Button/button_sync2_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X41Y45         FDCE (Prop_fdce_C_Q)         0.141     0.727 r  U_Button/button_sync2_reg[0]/Q
                         net (fo=21, routed)          0.132     0.859    U_Button/button_sync2_reg_n_0_[0]
    SLICE_X40Y45         LUT4 (Prop_lut4_I0_O)        0.045     0.904 r  U_Button/debounce_gen[0].debounce_cnt[0][15]_i_1/O
                         net (fo=1, routed)           0.000     0.904    U_Button/debounce_gen[0].debounce_cnt[0][15]_i_1_n_0
    SLICE_X40Y45         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][15]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.116    -0.032    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.024 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.323    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.352 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.833     1.185    U_Button/cpu_clk_BUFG
    SLICE_X40Y45         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][15]/C
                         clock pessimism             -0.586     0.599    
    SLICE_X40Y45         FDCE (Hold_fdce_C_D)         0.092     0.691    U_Button/debounce_gen[0].debounce_cnt_reg[0][15]
  -------------------------------------------------------------------
                         required time                         -0.691    
                         arrival time                           0.904    
  -------------------------------------------------------------------
                         slack                                  0.213    

Slack (MET) :             0.213ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[0]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[0].debounce_cnt_reg[0][14]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.317ns  (logic 0.186ns (58.636%)  route 0.131ns (41.364%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.185ns
    Source Clock Delay      (SCD):    0.586ns
    Clock Pessimism Removal (CPR):    0.586ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.796    -0.314    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.269 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.003    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     0.023 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.563     0.586    U_Button/cpu_clk_BUFG
    SLICE_X41Y45         FDCE                                         r  U_Button/button_sync2_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X41Y45         FDCE (Prop_fdce_C_Q)         0.141     0.727 r  U_Button/button_sync2_reg[0]/Q
                         net (fo=21, routed)          0.131     0.858    U_Button/button_sync2_reg_n_0_[0]
    SLICE_X40Y45         LUT4 (Prop_lut4_I0_O)        0.045     0.903 r  U_Button/debounce_gen[0].debounce_cnt[0][14]_i_1/O
                         net (fo=1, routed)           0.000     0.903    U_Button/debounce_gen[0].debounce_cnt[0][14]_i_1_n_0
    SLICE_X40Y45         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][14]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.116    -0.032    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.024 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.323    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.352 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.833     1.185    U_Button/cpu_clk_BUFG
    SLICE_X40Y45         FDCE                                         r  U_Button/debounce_gen[0].debounce_cnt_reg[0][14]/C
                         clock pessimism             -0.586     0.599    
    SLICE_X40Y45         FDCE (Hold_fdce_C_D)         0.091     0.690    U_Button/debounce_gen[0].debounce_cnt_reg[0][14]
  -------------------------------------------------------------------
                         required time                         -0.690    
                         arrival time                           0.903    
  -------------------------------------------------------------------
                         slack                                  0.213    

Slack (MET) :             0.220ns  (arrival time - required time)
  Source:                 U_Button/button_sync1_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/button_sync2_reg[1]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.306ns  (logic 0.141ns (46.101%)  route 0.165ns (53.899%))
  Logic Levels:           0  
  Clock Path Skew:        0.016ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.180ns
    Source Clock Delay      (SCD):    0.582ns
    Clock Pessimism Removal (CPR):    0.582ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.796    -0.314    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.269 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.003    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     0.023 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.559     0.582    U_Button/cpu_clk_BUFG
    SLICE_X36Y13         FDCE                                         r  U_Button/button_sync1_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X36Y13         FDCE (Prop_fdce_C_Q)         0.141     0.723 r  U_Button/button_sync1_reg[1]/Q
                         net (fo=1, routed)           0.165     0.888    U_Button/button_sync1[1]
    SLICE_X39Y12         FDCE                                         r  U_Button/button_sync2_reg[1]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.116    -0.032    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.024 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.323    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.352 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.828     1.180    U_Button/cpu_clk_BUFG
    SLICE_X39Y12         FDCE                                         r  U_Button/button_sync2_reg[1]/C
                         clock pessimism             -0.582     0.598    
    SLICE_X39Y12         FDCE (Hold_fdce_C_D)         0.070     0.668    U_Button/button_sync2_reg[1]
  -------------------------------------------------------------------
                         required time                         -0.668    
                         arrival time                           0.888    
  -------------------------------------------------------------------
                         slack                                  0.220    

Slack (MET) :             0.233ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][18]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.375ns  (logic 0.192ns (51.251%)  route 0.183ns (48.749%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.035ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.181ns
    Source Clock Delay      (SCD):    0.583ns
    Clock Pessimism Removal (CPR):    0.563ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.796    -0.314    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.269 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.003    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     0.023 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.560     0.583    U_Button/cpu_clk_BUFG
    SLICE_X39Y12         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y12         FDCE (Prop_fdce_C_Q)         0.141     0.724 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.183     0.907    U_Button/p_1_in8_in
    SLICE_X40Y12         LUT4 (Prop_lut4_I0_O)        0.051     0.958 r  U_Button/debounce_gen[1].debounce_cnt[1][18]_i_1/O
                         net (fo=1, routed)           0.000     0.958    U_Button/debounce_gen[1].debounce_cnt[1][18]_i_1_n_0
    SLICE_X40Y12         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][18]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.116    -0.032    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.024 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.323    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.352 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.829     1.181    U_Button/cpu_clk_BUFG
    SLICE_X40Y12         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][18]/C
                         clock pessimism             -0.563     0.618    
    SLICE_X40Y12         FDCE (Hold_fdce_C_D)         0.107     0.725    U_Button/debounce_gen[1].debounce_cnt_reg[1][18]
  -------------------------------------------------------------------
                         required time                         -0.725    
                         arrival time                           0.958    
  -------------------------------------------------------------------
                         slack                                  0.233    

Slack (MET) :             0.242ns  (arrival time - required time)
  Source:                 U_Button/button_sync2_reg[1]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[1].debounce_cnt_reg[1][14]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.369ns  (logic 0.186ns (50.457%)  route 0.183ns (49.543%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.035ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.181ns
    Source Clock Delay      (SCD):    0.583ns
    Clock Pessimism Removal (CPR):    0.563ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.796    -0.314    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.269 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.003    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     0.023 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.560     0.583    U_Button/cpu_clk_BUFG
    SLICE_X39Y12         FDCE                                         r  U_Button/button_sync2_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y12         FDCE (Prop_fdce_C_Q)         0.141     0.724 r  U_Button/button_sync2_reg[1]/Q
                         net (fo=21, routed)          0.183     0.907    U_Button/p_1_in8_in
    SLICE_X40Y12         LUT4 (Prop_lut4_I0_O)        0.045     0.952 r  U_Button/debounce_gen[1].debounce_cnt[1][14]_i_1/O
                         net (fo=1, routed)           0.000     0.952    U_Button/debounce_gen[1].debounce_cnt[1][14]_i_1_n_0
    SLICE_X40Y12         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][14]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.116    -0.032    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.024 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.323    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.352 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.829     1.181    U_Button/cpu_clk_BUFG
    SLICE_X40Y12         FDCE                                         r  U_Button/debounce_gen[1].debounce_cnt_reg[1][14]/C
                         clock pessimism             -0.563     0.618    
    SLICE_X40Y12         FDCE (Hold_fdce_C_D)         0.092     0.710    U_Button/debounce_gen[1].debounce_cnt_reg[1][14]
  -------------------------------------------------------------------
                         required time                         -0.710    
                         arrival time                           0.952    
  -------------------------------------------------------------------
                         slack                                  0.242    

Slack (MET) :             0.247ns  (arrival time - required time)
  Source:                 U_Button/debounce_gen[2].button_stable_reg[2]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[2].debounce_cnt_reg[2][5]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.369ns  (logic 0.189ns (51.174%)  route 0.180ns (48.826%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.015ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.179ns
    Source Clock Delay      (SCD):    0.581ns
    Clock Pessimism Removal (CPR):    0.583ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.796    -0.314    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.269 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.003    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     0.023 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.558     0.581    U_Button/cpu_clk_BUFG
    SLICE_X41Y33         FDCE                                         r  U_Button/debounce_gen[2].button_stable_reg[2]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X41Y33         FDCE (Prop_fdce_C_Q)         0.141     0.722 r  U_Button/debounce_gen[2].button_stable_reg[2]/Q
                         net (fo=22, routed)          0.180     0.902    U_Button/data0[2]
    SLICE_X41Y34         LUT4 (Prop_lut4_I1_O)        0.048     0.950 r  U_Button/debounce_gen[2].debounce_cnt[2][5]_i_1/O
                         net (fo=1, routed)           0.000     0.950    U_Button/debounce_gen[2].debounce_cnt[2][5]_i_1_n_0
    SLICE_X41Y34         FDCE                                         r  U_Button/debounce_gen[2].debounce_cnt_reg[2][5]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.116    -0.032    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.024 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.323    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.352 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.827     1.179    U_Button/cpu_clk_BUFG
    SLICE_X41Y34         FDCE                                         r  U_Button/debounce_gen[2].debounce_cnt_reg[2][5]/C
                         clock pessimism             -0.583     0.596    
    SLICE_X41Y34         FDCE (Hold_fdce_C_D)         0.107     0.703    U_Button/debounce_gen[2].debounce_cnt_reg[2][5]
  -------------------------------------------------------------------
                         required time                         -0.703    
                         arrival time                           0.950    
  -------------------------------------------------------------------
                         slack                                  0.247    

Slack (MET) :             0.249ns  (arrival time - required time)
  Source:                 U_Button/button_sync1_reg[4]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/button_sync2_reg[4]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.315ns  (logic 0.141ns (44.783%)  route 0.174ns (55.217%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.180ns
    Source Clock Delay      (SCD):    0.583ns
    Clock Pessimism Removal (CPR):    0.597ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.796    -0.314    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.269 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.003    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     0.023 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.560     0.583    U_Button/cpu_clk_BUFG
    SLICE_X48Y32         FDCE                                         r  U_Button/button_sync1_reg[4]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X48Y32         FDCE (Prop_fdce_C_Q)         0.141     0.724 r  U_Button/button_sync1_reg[4]/Q
                         net (fo=1, routed)           0.174     0.898    U_Button/button_sync1[4]
    SLICE_X48Y32         FDCE                                         r  U_Button/button_sync2_reg[4]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.116    -0.032    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.024 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.323    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.352 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.828     1.180    U_Button/cpu_clk_BUFG
    SLICE_X48Y32         FDCE                                         r  U_Button/button_sync2_reg[4]/C
                         clock pessimism             -0.597     0.583    
    SLICE_X48Y32         FDCE (Hold_fdce_C_D)         0.066     0.649    U_Button/button_sync2_reg[4]
  -------------------------------------------------------------------
                         required time                         -0.649    
                         arrival time                           0.898    
  -------------------------------------------------------------------
                         slack                                  0.249    

Slack (MET) :             0.255ns  (arrival time - required time)
  Source:                 U_Button/debounce_gen[3].button_stable_reg[3]/C
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Destination:            U_Button/debounce_gen[3].debounce_cnt_reg[3][1]/D
                            (rising edge-triggered cell FDCE clocked by clk_out1_cpuclk  {rise@0.000ns fall@20.000ns period=40.000ns})
  Path Group:             clk_out1_cpuclk
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_out1_cpuclk rise@0.000ns - clk_out1_cpuclk rise@0.000ns)
  Data Path Delay:        0.362ns  (logic 0.183ns (50.525%)  route 0.179ns (49.475%))
  Logic Levels:           1  (LUT4=1)
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.183ns
    Source Clock Delay      (SCD):    0.584ns
    Clock Pessimism Removal (CPR):    0.599ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.246     0.246 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.440     0.686    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.326    -1.639 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.504    -1.135    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.026    -1.109 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           0.796    -0.314    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.045    -0.269 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.266    -0.003    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.026     0.023 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.561     0.584    U_Button/cpu_clk_BUFG
    SLICE_X41Y39         FDCE                                         r  U_Button/debounce_gen[3].button_stable_reg[3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X41Y39         FDCE (Prop_fdce_C_Q)         0.141     0.725 r  U_Button/debounce_gen[3].button_stable_reg[3]/Q
                         net (fo=22, routed)          0.179     0.904    U_Button/data0[3]
    SLICE_X41Y39         LUT4 (Prop_lut4_I1_O)        0.042     0.946 r  U_Button/debounce_gen[3].debounce_cnt[3][1]_i_1/O
                         net (fo=1, routed)           0.000     0.946    U_Button/debounce_gen[3].debounce_cnt[3][1]_i_1_n_0
    SLICE_X41Y39         FDCE                                         r  U_Button/debounce_gen[3].debounce_cnt_reg[3][1]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_out1_cpuclk rise edge)
                                                      0.000     0.000 r  
    P17                                               0.000     0.000 r  fpga_clk (IN)
                         net (fo=0)                   0.000     0.000    Clkgen/inst/clk_in1
    P17                  IBUF (Prop_ibuf_I_O)         0.434     0.434 r  Clkgen/inst/clkin1_ibufg/O
                         net (fo=1, routed)           0.481     0.915    Clkgen/inst/clk_in1_cpuclk
    PLLE2_ADV_X0Y0       PLLE2_ADV (Prop_plle2_adv_CLKIN1_CLKOUT0)
                                                     -2.641    -1.726 r  Clkgen/inst/plle2_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.550    -1.176    Clkgen/inst/clk_out1_cpuclk
    BUFGCTRL_X0Y2        BUFG (Prop_bufg_I_O)         0.029    -1.147 r  Clkgen/inst/clkout1_buf/O
                         net (fo=1, routed)           1.116    -0.032    pll_clk
    SLICE_X36Y46         LUT2 (Prop_lut2_I0_O)        0.056     0.024 r  cpu_clk_BUFG_inst_i_1/O
                         net (fo=1, routed)           0.299     0.323    cpu_clk
    BUFGCTRL_X0Y0        BUFG (Prop_bufg_I_O)         0.029     0.352 r  cpu_clk_BUFG_inst/O
                         net (fo=8598, routed)        0.831     1.183    U_Button/cpu_clk_BUFG
    SLICE_X41Y39         FDCE                                         r  U_Button/debounce_gen[3].debounce_cnt_reg[3][1]/C
                         clock pessimism             -0.599     0.584    
    SLICE_X41Y39         FDCE (Hold_fdce_C_D)         0.107     0.691    U_Button/debounce_gen[3].debounce_cnt_reg[3][1]
  -------------------------------------------------------------------
                         required time                         -0.691    
                         arrival time                           0.946    
  -------------------------------------------------------------------
                         slack                                  0.255    





Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clk_out1_cpuclk
Waveform(ns):       { 0.000 20.000 }
Period(ns):         40.000
Sources:            { Clkgen/inst/plle2_adv_inst/CLKOUT0 }

Check Type        Corner  Lib Pin            Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     BUFG/I             n/a            2.155         40.000      37.845     BUFGCTRL_X0Y2   Clkgen/inst/clkout1_buf/I
Min Period        n/a     BUFG/I             n/a            2.155         40.000      37.845     BUFGCTRL_X0Y0   cpu_clk_BUFG_inst/I
Min Period        n/a     PLLE2_ADV/CLKOUT0  n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKOUT0
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X48Y76    Core_cpu/U_PC/pc_reg[0]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X51Y79    Core_cpu/U_PC/pc_reg[10]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X51Y79    Core_cpu/U_PC/pc_reg[11]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X51Y80    Core_cpu/U_PC/pc_reg[12]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X51Y80    Core_cpu/U_PC/pc_reg[13]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X47Y80    Core_cpu/U_PC/pc_reg[14]/C
Min Period        n/a     FDCE/C             n/a            1.000         40.000      39.000     SLICE_X51Y80    Core_cpu/U_PC/pc_reg[15]/C
Max Period        n/a     PLLE2_ADV/CLKOUT0  n/a            160.000       40.000      120.000    PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKOUT0
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X54Y130   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_31_31/RAMS64E_A/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X54Y130   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_31_31/RAMS64E_B/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X54Y130   Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_31_31/RAMS64E_C/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y26    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_11_11/RAMS64E_C/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X30Y26    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_11_11/RAMS64E_D/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X64Y22    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14336_14591_14_14/RAMS64E_A/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X64Y22    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14336_14591_14_14/RAMS64E_B/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X64Y22    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14336_14591_14_14/RAMS64E_C/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X64Y22    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_14336_14591_14_14/RAMS64E_D/CLK
Low Pulse Width   Fast    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X64Y44    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_16128_16383_6_6/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X50Y93    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_30_30/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X50Y93    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_30_30/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X50Y93    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_10240_10495_30_30/RAMS64E_D/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X38Y11    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X38Y11    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X38Y11    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_C/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X38Y11    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_11264_11519_12_12/RAMS64E_D/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X60Y67    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_22_22/RAMS64E_A/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X60Y67    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_22_22/RAMS64E_B/CLK
High Pulse Width  Slow    RAMS64E/CLK        n/a            1.250         20.000      18.750     SLICE_X60Y67    Mem_DRAM/U0/synth_options.dist_mem_inst/gen_sp_ram.spram_inst/ram_reg_12288_12543_22_22/RAMS64E_C/CLK



---------------------------------------------------------------------------------------------------
From Clock:  clkfbout_cpuclk
  To Clock:  clkfbout_cpuclk

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack       12.633ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clkfbout_cpuclk
Waveform(ns):       { 0.000 20.000 }
Period(ns):         40.000
Sources:            { Clkgen/inst/plle2_adv_inst/CLKFBOUT }

Check Type  Corner  Lib Pin             Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period  n/a     BUFG/I              n/a            2.155         40.000      37.845     BUFGCTRL_X0Y1   Clkgen/inst/clkf_buf/I
Min Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBOUT
Min Period  n/a     PLLE2_ADV/CLKFBIN   n/a            1.249         40.000      38.751     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBIN   n/a            52.633        40.000      12.633     PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            160.000       40.000      120.000    PLLE2_ADV_X0Y0  Clkgen/inst/plle2_adv_inst/CLKFBOUT



