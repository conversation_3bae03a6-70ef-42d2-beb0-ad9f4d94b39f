`timescale 1ns / 1ps

`include "defines.vh"

module myCPU (
    input  wire         cpu_rst,
    input  wire         cpu_clk,

    // Interface to IROM
`ifdef RUN_TRACE
    output wire [15:0]  inst_addr,
`else
    output wire [13:0]  inst_addr,
`endif
    input  wire [31:0]  inst,
    
    // Interface to Bridge
    output wire [31:0]  Bus_addr,
    input  wire [31:0]  Bus_rdata,
    output wire         Bus_we,
    output wire [31:0]  Bus_wdata

`ifdef RUN_TRACE
    ,// Debug Interface
    output wire         debug_wb_have_inst,
    output wire [31:0]  debug_wb_pc,
    output              debug_wb_ena,
    output wire [ 4:0]  debug_wb_reg,
    output wire [31:0]  debug_wb_value
`endif
);

    // ========== 数据通路信号定义 ==========

    // PC相关信号
    wire [31:0] PC_pc;          // 当前PC值
    wire [31:0] NPC_npc;        // 下一个PC值

    // 寄存器堆信号
    wire [31:0] RF_rD1;         // rs1读数据
    wire [31:0] RF_rD2;         // rs2读数据
    wire [31:0] RF_wD;          // 写数据
    wire        RF_we;          // 写使能

    // ALU信号
    wire [31:0] ALU_A;          // ALU输入A
    wire [31:0] ALU_B;          // ALU输入B
    wire [31:0] ALU_C;          // ALU输出
    wire [3:0]  ALU_op;         // ALU操作码

    // 立即数扩展信号
    wire [31:0] SEXT_ext;       // 扩展后的立即数
    wire [2:0]  SEXT_op;        // 立即数类型选择

    // 控制信号
    wire [1:0]  npc_op;         // NPC操作选择
    wire        rf_we;          // 寄存器写使能
    wire [1:0]  rf_wsel;        // 写回数据选择
    wire        alu_src;        // ALU第二个操作数选择
    wire        mem_we;         // 内存写使能

    // 指令字段
    wire [6:0]  opcode = inst[6:0];
    wire [4:0]  rd     = inst[11:7];
    wire [2:0]  funct3 = inst[14:12];
    wire [4:0]  rs1    = inst[19:15];
    wire [4:0]  rs2    = inst[24:20];
    wire [6:0]  funct7 = inst[31:25];

    // ========== 功能部件实例化 ==========

    // PC模块
    PC U_PC (
        .rst        (cpu_rst),
        .clk        (cpu_clk),
        .din        (NPC_npc),
        .pc         (PC_pc)
    );

    // NPC模块
    NPC U_NPC (
        .PC         (PC_pc),
        .IMM        (SEXT_ext),
        .ALU_C      (ALU_C),
        .npc_op     (npc_op),
        .npc        (NPC_npc)
    );

    // 寄存器堆模块
    RF U_RF (
        .clk        (cpu_clk),
        .rR1        (rs1),
        .rD1        (RF_rD1),
        .rR2        (rs2),
        .rD2        (RF_rD2),
        .we         (RF_we),
        .wR         (rd),
        .wD         (RF_wD)
    );

    // 立即数扩展模块
    SEXT U_SEXT (
        .inst       (inst),
        .sext_op    (SEXT_op),
        .ext        (SEXT_ext)
    );

    // ALU模块
    ALU U_ALU (
        .A          (ALU_A),
        .B          (ALU_B),
        .alu_op     (ALU_op),
        .C          (ALU_C)
    );

    // 控制单元
    Ctrl U_Ctrl (
        .opcode     (opcode),
        .funct3     (funct3),
        .funct7     (funct7),
        .alu_result (ALU_C),
        .npc_op     (npc_op),
        .rf_we      (rf_we),
        .rf_wsel    (rf_wsel),
        .sext_op    (SEXT_op),
        .alu_op     (ALU_op),
        .alu_src    (alu_src),
        .mem_we     (mem_we)
    );

    // ========== 数据通路连接 ==========

    // ALU输入连接
    assign ALU_A = RF_rD1;                          // ALU第一个操作数始终是rs1
    assign ALU_B = alu_src ? SEXT_ext : RF_rD2;     // ALU第二个操作数：立即数或rs2

    // 寄存器堆写使能和写数据选择
    assign RF_we = rf_we;
    assign RF_wD = (rf_wsel == `WB_ALU) ? ALU_C :
                   (rf_wsel == `WB_MEM) ? Bus_rdata :
                   (rf_wsel == `WB_PC4) ? (PC_pc + 4) :
                   (rf_wsel == `WB_IMM) ? SEXT_ext : 32'h0;

    // ========== 外部接口连接 ==========

    // 指令地址接口
`ifdef RUN_TRACE
    assign inst_addr = PC_pc[15:0];
`else
    assign inst_addr = PC_pc[15:2];     // 字地址，去掉低2位
`endif

    // 数据存储器接口
    assign Bus_addr  = ALU_C;           // 内存地址来自ALU计算结果
    assign Bus_we    = mem_we;          // 内存写使能
    assign Bus_wdata = RF_rD2;          // 写数据来自rs2

`ifdef RUN_TRACE
    // Debug Interface
    assign debug_wb_have_inst = ~cpu_rst;
    assign debug_wb_pc        = PC_pc;
    assign debug_wb_ena       = RF_we;
    assign debug_wb_reg       = rd;
    assign debug_wb_value     = RF_wD;
`endif

endmodule
