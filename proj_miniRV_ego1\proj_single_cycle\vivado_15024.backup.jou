#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Sat Jul  5 08:51:02 2025
# Process ID: 15024
# Current directory: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent16780 C:\Users\<USER>\Desktop\实验\cpu设计\lab2\proj_miniRV_ego1\proj_single_cycle\proj_single_cycle.xpr
# Log file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/vivado.log
# Journal file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle\vivado.jou
#-----------------------------------------------------------
start_gui
open_project C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.xpr
update_compile_order -fileset sources_1
open_hw
connect_hw_server
open_hw_target
set_property PROGRAM.FILE {C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
current_hw_device [get_hw_devices xc7a35t_0]
refresh_hw_device -update_hw_probes false [lindex [get_hw_devices xc7a35t_0] 0]
reset_run impl_1 -prev_step 
launch_runs impl_1 -to_step write_bitstream -jobs 32
wait_on_run impl_1
