`timescale 1ns / 1ps

// 全面测试21条必做指令的testbench
module myCPU_tb();

    // 输入信号
    reg         cpu_rst;
    reg         cpu_clk;
    reg  [31:0] inst;
    reg  [31:0] Bus_rdata;

    // 输出信号
    wire [13:0] inst_addr;
    wire [31:0] Bus_addr;
    wire        Bus_we;
    wire [31:0] Bus_wdata;

    // 测试计数器
    integer test_count;
    integer error_count;

    // 实例化被测试模块
    myCPU uut (
        .cpu_rst    (cpu_rst),
        .cpu_clk    (cpu_clk),
        .inst_addr  (inst_addr),
        .inst       (inst),
        .Bus_addr   (Bus_addr),
        .Bus_rdata  (Bus_rdata),
        .Bus_we     (Bus_we),
        .Bus_wdata  (Bus_wdata)
    );

    // 时钟生成
    initial begin
        cpu_clk = 0;
        forever #5 cpu_clk = ~cpu_clk;  // 100MHz时钟
    end

    // 测试任务
    task test_instruction;
        input [31:0] instruction;
        input [255:0] inst_name;
        begin
            inst = instruction;
            #10;  // 等待一个时钟周期
            test_count = test_count + 1;
            $display("测试 %0d: %s - 指令: %h, PC: %h, ALU结果: %h",
                     test_count, inst_name, instruction, {18'b0, inst_addr, 2'b0}, Bus_addr);
        end
    endtask

    // 主测试序列
    initial begin
        // 初始化
        test_count = 0;
        error_count = 0;
        cpu_rst = 1;
        inst = 32'h00000013;    // nop
        Bus_rdata = 32'h0;

        $display("=== 开始CPU指令测试 ===");

        // 复位
        #20;
        cpu_rst = 0;
        #10;
