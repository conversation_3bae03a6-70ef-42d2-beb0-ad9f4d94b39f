`timescale 1ns / 1ps

`include "defines.vh"

module myCPU (
    input  wire         cpu_rst,
    input  wire         cpu_clk,

    // Interface to IROM
`ifdef RUN_TRACE
    output wire [15:0]  inst_addr,
`else
    output wire [13:0]  inst_addr,
`endif
    input  wire [31:0]  inst,

    // Interface to Bridge
    output wire [31:0]  Bus_addr,
    input  wire [31:0]  Bus_rdata,
    output wire         Bus_we,
    output wire [31:0]  Bus_wdata

`ifdef RUN_TRACE
    ,// Debug Interface
    output wire         debug_wb_have_inst,
    output wire [31:0]  debug_wb_pc,
    output              debug_wb_ena,
    output wire [ 4:0]  debug_wb_reg,
    output wire [31:0]  debug_wb_value
`endif
);

    // Internal wires
    wire [31:0] pc_current;
    wire [31:0] pc_next;
    wire [31:0] pc_plus4;
    wire [31:0] pc_branch;
    wire [31:0] pc_jump;

    // Register file signals
    wire [31:0] rf_rdata1;
    wire [31:0] rf_rdata2;
    wire [31:0] rf_wdata;
    wire        rf_we;
    wire [4:0]  rf_waddr;

    // ALU signals
    wire [31:0] alu_result;
    wire [31:0] alu_src1;
    wire [31:0] alu_src2;
    wire [3:0]  alu_op;
    wire        alu_zero;

    // Immediate extension
    wire [31:0] imm_ext;

    // Control signals
    wire        branch;
    wire        jump;
    wire        mem_read;
    wire        mem_write;
    wire        reg_write;
    wire        mem_to_reg;
    wire        alu_src;
    wire [1:0]  pc_src;
    wire [2:0]  imm_sel;

    // Instruction fields
    wire [6:0]  opcode = inst[6:0];
    wire [4:0]  rd     = inst[11:7];
    wire [2:0]  funct3 = inst[14:12];
    wire [4:0]  rs1    = inst[19:15];
    wire [4:0]  rs2    = inst[24:20];
    wire [6:0]  funct7 = inst[31:25];

    // PC logic
    assign pc_plus4 = pc_current + 32'd4;
    assign pc_branch = pc_current + imm_ext;
    assign pc_jump = alu_result;

    reg [31:0] pc_next_reg;
    always @(*) begin
        case (pc_src)
            2'b00: pc_next_reg = pc_plus4;      // Normal increment
            2'b01: pc_next_reg = pc_branch;     // Branch
            2'b10: pc_next_reg = pc_jump;       // Jump (JAL/JALR)
            default: pc_next_reg = pc_plus4;
        endcase
    end
    assign pc_next = pc_next_reg;

    // PC register
    reg [31:0] pc_reg;
    always @(posedge cpu_clk or posedge cpu_rst) begin
        if (cpu_rst)
            pc_reg <= 32'h0000_0000;
        else
            pc_reg <= pc_next;
    end
    assign pc_current = pc_reg;

    // Instruction address output
`ifdef RUN_TRACE
    assign inst_addr = pc_current[15:0];
`else
    assign inst_addr = pc_current[15:2];  // Word address for 14-bit output
`endif

    // Register File (32 x 32-bit registers)
    reg [31:0] registers [31:0];
    integer i;

    always @(posedge cpu_clk) begin
        if (cpu_rst) begin
            for (i = 0; i < 32; i = i + 1)
                registers[i] <= 32'b0;
        end else if (rf_we && rf_waddr != 5'b0) begin
            registers[rf_waddr] <= rf_wdata;
        end
    end

    assign rf_rdata1 = (rs1 == 5'b0) ? 32'b0 : registers[rs1];
    assign rf_rdata2 = (rs2 == 5'b0) ? 32'b0 : registers[rs2];
    assign rf_waddr = rd;

    // Immediate Extension Unit
    reg [31:0] imm_ext_reg;
    always @(*) begin
        case (imm_sel)
            `IMM_I: imm_ext_reg = {{20{inst[31]}}, inst[31:20]};                    // I-type
            `IMM_S: imm_ext_reg = {{20{inst[31]}}, inst[31:25], inst[11:7]};       // S-type
            `IMM_B: imm_ext_reg = {{19{inst[31]}}, inst[31], inst[7], inst[30:25], inst[11:8], 1'b0}; // B-type
            `IMM_U: imm_ext_reg = {inst[31:12], 12'b0};                            // U-type
            `IMM_J: imm_ext_reg = {{11{inst[31]}}, inst[31], inst[19:12], inst[20], inst[30:21], 1'b0}; // J-type
            default: imm_ext_reg = 32'b0;
        endcase
    end
    assign imm_ext = imm_ext_reg;

    // ALU
    assign alu_src1 = rf_rdata1;
    assign alu_src2 = alu_src ? imm_ext : rf_rdata2;

    reg [31:0] alu_result_reg;
    always @(*) begin
        case (alu_op)
            `ALU_ADD:  alu_result_reg = alu_src1 + alu_src2;
            `ALU_SUB:  alu_result_reg = alu_src1 - alu_src2;
            `ALU_AND:  alu_result_reg = alu_src1 & alu_src2;
            `ALU_OR:   alu_result_reg = alu_src1 | alu_src2;
            `ALU_XOR:  alu_result_reg = alu_src1 ^ alu_src2;
            `ALU_SLL:  alu_result_reg = alu_src1 << alu_src2[4:0];
            `ALU_SRL:  alu_result_reg = alu_src1 >> alu_src2[4:0];
            `ALU_SRA:  alu_result_reg = $signed(alu_src1) >>> alu_src2[4:0];
            `ALU_SLT:  alu_result_reg = ($signed(alu_src1) < $signed(alu_src2)) ? 32'b1 : 32'b0;
            `ALU_SLTU: alu_result_reg = (alu_src1 < alu_src2) ? 32'b1 : 32'b0;
            default:   alu_result_reg = 32'b0;
        endcase
    end
    assign alu_result = alu_result_reg;
    assign alu_zero = (alu_result == 32'b0);

    // Control Unit
    reg        reg_write_reg;
    reg        mem_read_reg;
    reg        mem_write_reg;
    reg        mem_to_reg_reg;
    reg        alu_src_reg;
    reg [1:0]  pc_src_reg;
    reg [3:0]  alu_op_reg;
    reg [2:0]  imm_sel_reg;
    reg        branch_reg;
    reg        jump_reg;

    always @(*) begin
        // Default values
        reg_write_reg = 1'b0;
        mem_read_reg = 1'b0;
        mem_write_reg = 1'b0;
        mem_to_reg_reg = 1'b0;
        alu_src_reg = 1'b0;
        pc_src_reg = 2'b00;
        alu_op_reg = `ALU_ADD;
        imm_sel_reg = `IMM_I;
        branch_reg = 1'b0;
        jump_reg = 1'b0;

        case (opcode)
            `OP_LUI: begin      // LUI
                reg_write_reg = 1'b1;
                alu_src_reg = 1'b1;
                alu_op_reg = `ALU_ADD;
                imm_sel_reg = `IMM_U;
            end

            `OP_AUIPC: begin    // AUIPC
                reg_write_reg = 1'b1;
                alu_src_reg = 1'b1;
                alu_op_reg = `ALU_ADD;
                imm_sel_reg = `IMM_U;
            end

            `OP_JAL: begin      // JAL
                reg_write_reg = 1'b1;
                jump_reg = 1'b1;
                pc_src_reg = 2'b01;
                imm_sel_reg = `IMM_J;
            end

            `OP_JALR: begin     // JALR
                reg_write_reg = 1'b1;
                alu_src_reg = 1'b1;
                alu_op_reg = `ALU_ADD;
                jump_reg = 1'b1;
                pc_src_reg = 2'b10;
                imm_sel_reg = `IMM_I;
            end

            `OP_BRANCH: begin   // Branch instructions
                branch_reg = 1'b1;
                alu_op_reg = `ALU_SUB;
                imm_sel_reg = `IMM_B;
                case (funct3)
                    3'b000: pc_src_reg = alu_zero ? 2'b01 : 2'b00;        // BEQ
                    3'b001: pc_src_reg = ~alu_zero ? 2'b01 : 2'b00;       // BNE
                    3'b100: pc_src_reg = alu_result[31] ? 2'b01 : 2'b00;  // BLT
                    3'b101: pc_src_reg = ~alu_result[31] ? 2'b01 : 2'b00; // BGE
                    3'b110: pc_src_reg = alu_result[31] ? 2'b01 : 2'b00;  // BLTU
                    3'b111: pc_src_reg = ~alu_result[31] ? 2'b01 : 2'b00; // BGEU
                    default: pc_src_reg = 2'b00;
                endcase
            end

            `OP_LOAD: begin     // Load instructions
                reg_write_reg = 1'b1;
                mem_read_reg = 1'b1;
                mem_to_reg_reg = 1'b1;
                alu_src_reg = 1'b1;
                alu_op_reg = `ALU_ADD;
                imm_sel_reg = `IMM_I;
            end

            `OP_STORE: begin    // Store instructions
                mem_write_reg = 1'b1;
                alu_src_reg = 1'b1;
                alu_op_reg = `ALU_ADD;
                imm_sel_reg = `IMM_S;
            end

            `OP_IMM: begin      // Immediate arithmetic
                reg_write_reg = 1'b1;
                alu_src_reg = 1'b1;
                imm_sel_reg = `IMM_I;
                case (funct3)
                    3'b000: alu_op_reg = `ALU_ADD;   // ADDI
                    3'b010: alu_op_reg = `ALU_SLT;   // SLTI
                    3'b011: alu_op_reg = `ALU_SLTU;  // SLTIU
                    3'b100: alu_op_reg = `ALU_XOR;   // XORI
                    3'b110: alu_op_reg = `ALU_OR;    // ORI
                    3'b111: alu_op_reg = `ALU_AND;   // ANDI
                    3'b001: alu_op_reg = `ALU_SLL;   // SLLI
                    3'b101: alu_op_reg = (funct7[5]) ? `ALU_SRA : `ALU_SRL; // SRLI/SRAI
                    default: alu_op_reg = `ALU_ADD;
                endcase
            end

            `OP_REG: begin      // Register arithmetic
                reg_write_reg = 1'b1;
                case (funct3)
                    3'b000: alu_op_reg = (funct7[5]) ? `ALU_SUB : `ALU_ADD; // ADD/SUB
                    3'b001: alu_op_reg = `ALU_SLL;   // SLL
                    3'b010: alu_op_reg = `ALU_SLT;   // SLT
                    3'b011: alu_op_reg = `ALU_SLTU;  // SLTU
                    3'b100: alu_op_reg = `ALU_XOR;   // XOR
                    3'b101: alu_op_reg = (funct7[5]) ? `ALU_SRA : `ALU_SRL; // SRL/SRA
                    3'b110: alu_op_reg = `ALU_OR;    // OR
                    3'b111: alu_op_reg = `ALU_AND;   // AND
                    default: alu_op_reg = `ALU_ADD;
                endcase
            end

            default: begin
                // NOP or unknown instruction
            end
        endcase
    end

    // Assign control signals
    assign reg_write = reg_write_reg;
    assign mem_read = mem_read_reg;
    assign mem_write = mem_write_reg;
    assign mem_to_reg = mem_to_reg_reg;
    assign alu_src = alu_src_reg;
    assign pc_src = pc_src_reg;
    assign alu_op = alu_op_reg;
    assign imm_sel = imm_sel_reg;
    assign branch = branch_reg;
    assign jump = jump_reg;

    // Write-back data selection
    assign rf_wdata = mem_to_reg ? Bus_rdata :
                      jump ? pc_plus4 :
                      (opcode == `OP_AUIPC) ? (pc_current + imm_ext) :
                      (opcode == `OP_LUI) ? imm_ext : alu_result;
    assign rf_we = reg_write;

    // Memory interface
    assign Bus_addr = alu_result;
    assign Bus_wdata = rf_rdata2;
    assign Bus_we = mem_write;

`ifdef RUN_TRACE
    // Debug Interface
    assign debug_wb_have_inst = ~cpu_rst;
    assign debug_wb_pc        = pc_current;
    assign debug_wb_ena       = rf_we;
    assign debug_wb_reg       = rf_waddr;
    assign debug_wb_value     = rf_wdata;
`endif

endmodule
