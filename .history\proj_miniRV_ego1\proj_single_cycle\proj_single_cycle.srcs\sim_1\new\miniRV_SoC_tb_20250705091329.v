`timescale 1ns / 1ps

// 完整SoC系统测试bench，测试LFSR随机数生成和排序程序
module miniRV_SoC_tb();

    // 输入信号
    reg         fpga_rst;
    reg         fpga_clk;
    reg  [15:0] sw;
    reg  [4:0]  button;
    
    // 输出信号
    wire        cpu_clk;
    wire [15:0] led;
    wire [7:0]  dig_en;
    wire [7:0]  DN_A;
    wire [7:0]  DN_B;
    wire [7:0]  DN_C;
    wire [7:0]  DN_D;
    wire [7:0]  DN_E;
    wire [7:0]  DN_F;
    wire [7:0]  DN_G;
    wire [7:0]  DN_DP;
    
    // 实例化被测试模块
    miniRV_SoC uut (
        .fpga_rst   (fpga_rst),
        .fpga_clk   (fpga_clk),
        .sw         (sw),
        .button     (button),
        .led        (led),
        .dig_en     (dig_en),
        .DN_A       (DN_A),
        .DN_B       (DN_B),
        .DN_C       (DN_C),
        .DN_D       (DN_D),
        .DN_E       (DN_E),
        .DN_F       (DN_F),
        .DN_G       (DN_G),
        .DN_DP      (DN_DP)
    );
    
    // 时钟生成 (100MHz FPGA时钟)
    initial begin
        fpga_clk = 0;
        forever #5 fpga_clk = ~fpga_clk;
    end
    
    // 测试任务：模拟拨码开关操作
    task set_switch;
        input [1:0] sw_val;
        begin
            sw[1:0] = sw_val;
            #100000;  // 等待足够长时间让CPU处理
        end
    endtask
    
    // 监控CPU状态
    always @(posedge cpu_clk) begin
        if (!fpga_rst) begin
            // 监控关键信号
            if (uut.u_Bridge.addr_cpu2bridge >= 32'hFFFFF000) begin
                $display("时间: %0t, PC: %h, 外设访问: 地址=%h, 写使能=%b, 写数据=%h, 读数据=%h", 
                         $time, 
                         {18'b0, uut.u_myCPU.inst_addr, 2'b0},
                         uut.u_Bridge.addr_cpu2bridge,
                         uut.u_Bridge.wen_cpu2bridge,
                         uut.u_Bridge.wdata_cpu2bridge,
                         uut.u_Bridge.rdata_bridge2cpu);
            end
        end
    end
    
    // 主测试序列
    initial begin
        $display("=== 开始miniRV SoC系统测试 ===");
        
        // 初始化
        fpga_rst = 1;
        sw = 16'h0;
        button = 5'h0;
        
        // 复位
        #1000;
        fpga_rst = 0;
        
        $display("阶段0: 计时器显示阶段");
        set_switch(2'b00);  // SW[1:0] = 00，保持在阶段0
        
        $display("切换到阶段1: 锁定种子");
        set_switch(2'b01);  // SW[1:0] = 01，进入阶段1
        
        $display("等待种子锁定...");
        #500000;  // 等待种子锁定
        
        $display("切换到阶段2: 随机数生成");
        set_switch(2'b10);  // SW[1:0] = 10，进入阶段2
        
        $display("等待随机数生成完成...");
        #1000000;  // 等待随机数生成和显示
        
        $display("切换到阶段3: 排序并点亮LED");
        set_switch(2'b11);  // SW[1:0] = 11，进入阶段3
        
        $display("等待排序完成...");
        #1000000;  // 等待排序完成
        
        $display("返回阶段4: 显示排序结果");
        set_switch(2'b00);  // SW[1:0] = 00，进入阶段4
        
        $display("观察最终结果...");
        #500000;
        
        $display("=== 测试完成 ===");
        $display("LED状态: %b", led);
        $display("数码管显示使能: %b", dig_en);
        
        $finish;
    end
    
    // 监控LED变化
    always @(led) begin
        if (led != 16'h0) begin
            $display("LED点亮: %b (时间: %0t)", led, $time);
        end
    end
    
    // 监控数码管变化
    reg [31:0] last_dig_data;
    wire [31:0] current_dig_data = {DN_A[0], DN_B[0], DN_C[0], DN_D[0], 
                                   DN_E[0], DN_F[0], DN_G[0], DN_DP[0],
                                   DN_A[1], DN_B[1], DN_C[1], DN_D[1], 
                                   DN_E[1], DN_F[1], DN_G[1], DN_DP[1],
                                   DN_A[2], DN_B[2], DN_C[2], DN_D[2], 
                                   DN_E[2], DN_F[2], DN_G[2], DN_DP[2],
                                   DN_A[3], DN_B[3], DN_C[3], DN_D[3], 
                                   DN_E[3], DN_F[3], DN_G[3], DN_DP[3]};
    
    always @(current_dig_data) begin
        if (current_dig_data != last_dig_data) begin
            $display("数码管显示变化 (时间: %0t)", $time);
            last_dig_data = current_dig_data;
        end
    end

endmodule
