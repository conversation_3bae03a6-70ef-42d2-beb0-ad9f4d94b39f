#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Thu Jul  3 16:58:15 2025
# Process ID: 28860
# Current directory: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1
# Command line: vivado.exe -log IROM.vds -product Vivado -mode batch -messageDb vivado.pb -notrace -source IROM.tcl
# Log file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1/IROM.vds
# Journal file: C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1\vivado.jou
#-----------------------------------------------------------
source IROM.tcl -notrace
Command: synth_design -top IROM -part xc7a35tcsg324-1 -mode out_of_context
Starting synth_design
Attempting to get a license for feature 'Synthesis' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Synthesis' and/or device 'xc7a35t'
INFO: Launching helper process for spawning children vivado processes
INFO: Helper process launched with PID 22924 
---------------------------------------------------------------------------------
Starting RTL Elaboration : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 467.492 ; gain = 99.441
---------------------------------------------------------------------------------
INFO: [Synth 8-638] synthesizing module 'IROM' [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/synth/IROM.vhd:66]
	Parameter C_FAMILY bound to: artix7 - type: string 
	Parameter C_ADDR_WIDTH bound to: 14 - type: integer 
	Parameter C_DEFAULT_DATA bound to: 0 - type: string 
	Parameter C_DEPTH bound to: 16384 - type: integer 
	Parameter C_HAS_CLK bound to: 0 - type: integer 
	Parameter C_HAS_D bound to: 0 - type: integer 
	Parameter C_HAS_DPO bound to: 0 - type: integer 
	Parameter C_HAS_DPRA bound to: 0 - type: integer 
	Parameter C_HAS_I_CE bound to: 0 - type: integer 
	Parameter C_HAS_QDPO bound to: 0 - type: integer 
	Parameter C_HAS_QDPO_CE bound to: 0 - type: integer 
	Parameter C_HAS_QDPO_CLK bound to: 0 - type: integer 
	Parameter C_HAS_QDPO_RST bound to: 0 - type: integer 
	Parameter C_HAS_QDPO_SRST bound to: 0 - type: integer 
	Parameter C_HAS_QSPO bound to: 0 - type: integer 
	Parameter C_HAS_QSPO_CE bound to: 0 - type: integer 
	Parameter C_HAS_QSPO_RST bound to: 0 - type: integer 
	Parameter C_HAS_QSPO_SRST bound to: 0 - type: integer 
	Parameter C_HAS_SPO bound to: 1 - type: integer 
	Parameter C_HAS_WE bound to: 0 - type: integer 
	Parameter C_MEM_INIT_FILE bound to: IROM.mif - type: string 
	Parameter C_ELABORATION_DIR bound to: ./ - type: string 
	Parameter C_MEM_TYPE bound to: 0 - type: integer 
	Parameter C_PIPELINE_STAGES bound to: 0 - type: integer 
	Parameter C_QCE_JOINED bound to: 0 - type: integer 
	Parameter C_QUALIFY_WE bound to: 0 - type: integer 
	Parameter C_READ_MIF bound to: 1 - type: integer 
	Parameter C_REG_A_D_INPUTS bound to: 0 - type: integer 
	Parameter C_REG_DPRA_INPUT bound to: 0 - type: integer 
	Parameter C_SYNC_ENABLE bound to: 1 - type: integer 
	Parameter C_WIDTH bound to: 32 - type: integer 
	Parameter C_PARSER_TYPE bound to: 1 - type: integer 
INFO: [Synth 8-3491] module 'dist_mem_gen_v8_0_12' declared at 'c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/hdl/dist_mem_gen_v8_0_vhsyn_rfs.vhd:3237' bound to instance 'U0' of component 'dist_mem_gen_v8_0_12' [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/synth/IROM.vhd:132]
INFO: [Synth 8-256] done synthesizing module 'IROM' (4#1) [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/synth/IROM.vhd:66]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[31]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[30]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[29]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[28]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[27]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[26]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[25]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[24]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[23]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[22]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[21]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[20]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[19]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[18]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[17]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[16]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[15]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[14]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[13]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[12]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[11]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[10]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[9]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[8]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[7]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[6]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[5]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[4]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[3]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[2]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[1]
WARNING: [Synth 8-3331] design rom has unconnected port qspo[0]
WARNING: [Synth 8-3331] design rom has unconnected port clk
WARNING: [Synth 8-3331] design rom has unconnected port qspo_ce
WARNING: [Synth 8-3331] design rom has unconnected port qspo_rst
WARNING: [Synth 8-3331] design rom has unconnected port qspo_srst
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[31]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[30]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[29]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[28]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[27]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[26]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[25]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[24]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[23]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[22]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[21]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[20]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[19]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[18]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[17]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[16]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[15]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[14]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[13]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[12]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[11]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[10]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[9]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[8]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[7]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[6]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[5]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[4]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[3]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[2]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[1]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port dpo[0]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[31]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[30]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[29]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[28]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[27]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[26]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[25]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[24]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[23]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[22]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[21]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[20]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[19]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[18]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[17]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[16]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[15]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[14]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[13]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[12]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[11]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[10]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[9]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[8]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[7]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[6]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[5]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[4]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[3]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[2]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[1]
WARNING: [Synth 8-3331] design dist_mem_gen_v8_0_12_synth has unconnected port qdpo[0]
INFO: [Common 17-14] Message 'Synth 8-3331' appears 100 times and further instances of the messages will be disabled. Use the Tcl command set_msg_config to change the current settings.
---------------------------------------------------------------------------------
Finished RTL Elaboration : Time (s): cpu = 00:07:26 ; elapsed = 00:08:01 . Memory (MB): peak = 714.828 ; gain = 346.777
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:07:26 ; elapsed = 00:08:02 . Memory (MB): peak = 714.828 ; gain = 346.777
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 1 : Time (s): cpu = 00:07:26 ; elapsed = 00:08:02 . Memory (MB): peak = 714.828 ; gain = 346.777
---------------------------------------------------------------------------------
INFO: [Device 21-403] Loading part xc7a35tcsg324-1
INFO: [Project 1-570] Preparing netlist for logic optimization

Processing XDC Constraints
Initializing timing engine
Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM_ooc.xdc] for cell 'U0'
Finished Parsing XDC File [c:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM_ooc.xdc] for cell 'U0'
Parsing XDC File [C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1/dont_touch.xdc]
Finished Parsing XDC File [C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1/dont_touch.xdc]
Completed Processing XDC Constraints

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 796.047 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 796.047 ; gain = 0.000
Constraint Validation Runtime : Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.015 . Memory (MB): peak = 797.168 ; gain = 1.121
---------------------------------------------------------------------------------
Finished Constraint Validation : Time (s): cpu = 00:07:33 ; elapsed = 00:08:11 . Memory (MB): peak = 797.168 ; gain = 429.117
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Loading Part and Timing Information
---------------------------------------------------------------------------------
Loading part: xc7a35tcsg324-1
---------------------------------------------------------------------------------
Finished Loading Part and Timing Information : Time (s): cpu = 00:07:33 ; elapsed = 00:08:11 . Memory (MB): peak = 797.168 ; gain = 429.117
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Applying 'set_property' XDC Constraints
---------------------------------------------------------------------------------
Applied set_property DONT_TOUCH = true for U0. (constraint file  C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1/dont_touch.xdc, line 9).
---------------------------------------------------------------------------------
Finished applying 'set_property' XDC Constraints : Time (s): cpu = 00:07:33 ; elapsed = 00:08:11 . Memory (MB): peak = 797.168 ; gain = 429.117
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 2 : Time (s): cpu = 00:08:12 ; elapsed = 00:08:53 . Memory (MB): peak = 797.168 ; gain = 429.117
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start RTL Component Statistics 
---------------------------------------------------------------------------------
Detailed RTL Component Info : 
---------------------------------------------------------------------------------
Finished RTL Component Statistics 
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start RTL Hierarchical Component Statistics 
---------------------------------------------------------------------------------
Hierarchical RTL Component report 
---------------------------------------------------------------------------------
Finished RTL Hierarchical Component Statistics
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Part Resource Summary
---------------------------------------------------------------------------------
Part Resources:
DSPs: 90 (col length:60)
BRAMs: 100 (col length: RAMB18 60 RAMB36 30)
---------------------------------------------------------------------------------
Finished Part Resource Summary
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Cross Boundary and Area Optimization
---------------------------------------------------------------------------------
Warning: Parallel synthesis criteria is not met 
---------------------------------------------------------------------------------
Finished Cross Boundary and Area Optimization : Time (s): cpu = 00:08:54 ; elapsed = 00:09:38 . Memory (MB): peak = 797.168 ; gain = 429.117
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

ROM:
+------------+------------+---------------+----------------+
|Module Name | RTL Object | Depth x Width | Implemented As | 
+------------+------------+---------------+----------------+
|rom         | rom[16383] | 16384x32      | LUT            | 
|rom         | rom[16383] | 16384x32      | LUT            | 
+------------+------------+---------------+----------------+

---------------------------------------------------------------------------------
Finished ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Applying XDC Timing Constraints
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Applying XDC Timing Constraints : Time (s): cpu = 00:09:03 ; elapsed = 00:09:48 . Memory (MB): peak = 854.840 ; gain = 486.789
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Timing Optimization
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Timing Optimization : Time (s): cpu = 00:09:03 ; elapsed = 00:09:48 . Memory (MB): peak = 855.062 ; gain = 487.012
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Technology Mapping
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Technology Mapping : Time (s): cpu = 00:09:03 ; elapsed = 00:09:48 . Memory (MB): peak = 874.238 ; gain = 506.188
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished IO Insertion : Time (s): cpu = 00:09:03 ; elapsed = 00:09:49 . Memory (MB): peak = 874.238 ; gain = 506.188
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Renaming Generated Instances
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Instances : Time (s): cpu = 00:09:03 ; elapsed = 00:09:49 . Memory (MB): peak = 874.238 ; gain = 506.188
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Rebuilding User Hierarchy
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Rebuilding User Hierarchy : Time (s): cpu = 00:09:04 ; elapsed = 00:09:49 . Memory (MB): peak = 874.238 ; gain = 506.188
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Ports
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Ports : Time (s): cpu = 00:09:04 ; elapsed = 00:09:49 . Memory (MB): peak = 874.238 ; gain = 506.188
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:09:04 ; elapsed = 00:09:49 . Memory (MB): peak = 874.238 ; gain = 506.188
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Nets
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Nets : Time (s): cpu = 00:09:04 ; elapsed = 00:09:49 . Memory (MB): peak = 874.238 ; gain = 506.188
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Writing Synthesis Report
---------------------------------------------------------------------------------

Report BlackBoxes: 
+-+--------------+----------+
| |BlackBox name |Instances |
+-+--------------+----------+
+-+--------------+----------+

Report Cell Usage: 
+------+-----+------+
|      |Cell |Count |
+------+-----+------+
|1     |LUT4 |     1|
|2     |LUT5 |    27|
|3     |LUT6 |    80|
+------+-----+------+

Report Instance Areas: 
+------+----------------------------------+---------------------------+------+
|      |Instance                          |Module                     |Cells |
+------+----------------------------------+---------------------------+------+
|1     |top                               |                           |   108|
|2     |  U0                              |dist_mem_gen_v8_0_12       |   108|
|3     |    \synth_options.dist_mem_inst  |dist_mem_gen_v8_0_12_synth |   108|
|4     |      \gen_rom.rom_inst           |rom                        |   108|
+------+----------------------------------+---------------------------+------+
---------------------------------------------------------------------------------
Finished Writing Synthesis Report : Time (s): cpu = 00:09:04 ; elapsed = 00:09:49 . Memory (MB): peak = 874.238 ; gain = 506.188
---------------------------------------------------------------------------------
Synthesis finished with 0 errors, 0 critical warnings and 156 warnings.
Synthesis Optimization Runtime : Time (s): cpu = 00:01:33 ; elapsed = 00:09:43 . Memory (MB): peak = 874.238 ; gain = 423.848
Synthesis Optimization Complete : Time (s): cpu = 00:09:04 ; elapsed = 00:09:49 . Memory (MB): peak = 874.238 ; gain = 506.188
INFO: [Project 1-571] Translating synthesized netlist
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 881.867 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

INFO: [Common 17-83] Releasing license: Synthesis
13 Infos, 100 Warnings, 0 Critical Warnings and 0 Errors encountered.
synth_design completed successfully
synth_design: Time (s): cpu = 00:09:05 ; elapsed = 00:09:51 . Memory (MB): peak = 881.867 ; gain = 525.316
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 881.867 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1/IROM.dcp' has been generated.
INFO: [Coretcl 2-1648] Added synthesis output to IP cache for IP IROM, cache-ID = 3a5ea74b8d1e596c
INFO: [Coretcl 2-1174] Renamed 3 cell refs.
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 881.867 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'C:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1/IROM.dcp' has been generated.
INFO: [runtcl-4] Executing : report_utilization -file IROM_utilization_synth.rpt -pb IROM_utilization_synth.pb
INFO: [Common 17-206] Exiting Vivado at Thu Jul  3 17:08:09 2025...
