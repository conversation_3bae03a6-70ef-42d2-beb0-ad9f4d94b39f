`timescale 1ns / 1ps

`include "defines.vh"

// TODO: Ctrl模块 - 控制单元 - 严格按照设计规范实现
// 功能：根据指令的opcode、funct3、funct7生成控制信号
module Ctrl (
    input  wire [6:0]   opcode,     // 操作码
    input  wire [2:0]   funct3,     // 功能码3
    input  wire [6:0]   funct7,     // 功能码7
    input  wire         alu_f,      // ALU标志位（用于分支判断）
    output reg  [1:0]   npc_op,     // NPC操作选择
    output reg          rf_we,      // 寄存器写使能
    output reg  [1:0]   rf_wsel,    // 写回数据选择
    output reg  [2:0]   sext_op,    // 立即数类型选择
    output reg  [3:0]   alu_op,     // ALU操作码
    output reg          alub_sel,   // ALU第二个操作数选择
    output reg          ram_we      // 内存写使能
);

    // TODO: 根据指令类型生成控制信号 - 严格按照设计规范
    always @(*) begin
        // 默认值
        npc_op   = `NPC_PC4;
        rf_we    = 1'b0;
        rf_wsel  = `WB_ALU;
        sext_op  = `EXT_I;
        alu_op   = `ALU_ADD;
        alub_sel = `ALUB_RS2;
        ram_we   = 1'b0;

        case (opcode)
            `OP_LUI: begin              // lui
                npc_op   = `NPC_PC4;
                rf_we    = 1'b1;
                rf_wsel  = `WB_EXT;
                sext_op  = `EXT_U;
            end

            `OP_JAL: begin              // jal
                npc_op   = `NPC_PCIMM;
                rf_we    = 1'b1;
                rf_wsel  = `WB_PC4;
                sext_op  = `EXT_J;
            end

            `OP_JALR: begin             // jalr
                npc_op   = `NPC_RD1IMM;
                rf_we    = 1'b1;
                rf_wsel  = `WB_PC4;
                sext_op  = `EXT_I;
                alu_op   = `ALU_ADD;
                alub_sel = `ALUB_EXT;
            end

            `OP_BRANCH: begin           // beq, blt
                sext_op  = `EXT_B;
                alub_sel = `ALUB_RS2;
                case (funct3)
                    3'b000: begin       // beq
                        alu_op = `ALU_BEQ;
                        npc_op = alu_f ? `NPC_PCIMM : `NPC_PC4;
                    end
                    3'b100: begin       // blt
                        alu_op = `ALU_BLT;
                        npc_op = alu_f ? `NPC_PCIMM : `NPC_PC4;
                    end
                    default: begin
                        alu_op = `ALU_BEQ;
                        npc_op = `NPC_PC4;
                    end
                endcase
            end

            `OP_LOAD: begin             // lw
                npc_op   = `NPC_PC4;
                rf_we    = 1'b1;
                rf_wsel  = `WB_DRAM;
                sext_op  = `EXT_I;
                alu_op   = `ALU_ADD;
                alub_sel = `ALUB_EXT;
            end

            `OP_STORE: begin            // sw
                npc_op   = `NPC_PC4;
                rf_we    = 1'b0;
                sext_op  = `EXT_S;
                alu_op   = `ALU_ADD;
                alub_sel = `ALUB_EXT;
                ram_we   = 1'b1;
            end

            `OP_IMM: begin              // addi, andi, ori, xori, slli, srli, srai
                npc_op   = `NPC_PC4;
                rf_we    = 1'b1;
                rf_wsel  = `WB_ALU;
                alub_sel = `ALUB_EXT;
                case (funct3)
                    3'b000: begin       // addi
                        sext_op = `EXT_I;
                        alu_op  = `ALU_ADD;
                    end
                    3'b111: begin       // andi
                        sext_op = `EXT_I;
                        alu_op  = `ALU_AND;
                    end
                    3'b110: begin       // ori
                        sext_op = `EXT_I;
                        alu_op  = `ALU_OR;
                    end
                    3'b100: begin       // xori
                        sext_op = `EXT_I;
                        alu_op  = `ALU_XOR;
                    end
                    3'b001: begin       // slli
                        sext_op = `EXT_shift;
                        alu_op  = `ALU_SLL;
                    end
                    3'b101: begin       // srli, srai
                        sext_op = `EXT_shift;
                        if (funct7[5] == 1'b0)
                            alu_op = `ALU_SRL;  // srli
                        else
                            alu_op = `ALU_SRA;  // srai
                    end
                    default: begin
                        sext_op = `EXT_I;
                        alu_op  = `ALU_ADD;
                    end
                endcase
            end

            `OP_REG: begin              // add, and, or, xor, sll, srl, sra
                npc_op   = `NPC_PC4;
                rf_we    = 1'b1;
                rf_wsel  = `WB_ALU;
                alub_sel = `ALUB_RS2;
                case (funct3)
                    3'b000: begin       // add
                        if (funct7[5] == 1'b0)
                            alu_op = `ALU_ADD;  // add
                        else
                            alu_op = `ALU_SUB;  // sub (虽然不是必做指令)
                    end
                    3'b111: alu_op = `ALU_AND;  // and
                    3'b110: alu_op = `ALU_OR;   // or
                    3'b100: alu_op = `ALU_XOR;  // xor
                    3'b001: alu_op = `ALU_SLL;  // sll
                    3'b101: begin               // srl, sra
                        if (funct7[5] == 1'b0)
                            alu_op = `ALU_SRL;  // srl
                        else
                            alu_op = `ALU_SRA;  // sra
                    end
                    default: alu_op = `ALU_ADD;
                endcase
            end

            default: begin
                // 保持默认值
            end
        endcase
    end

endmodule
