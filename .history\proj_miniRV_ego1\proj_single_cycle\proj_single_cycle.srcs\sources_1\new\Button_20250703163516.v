`timescale 1ns / 1ps

`include "defines.vh"

// TODO: 按键开关接口模块
// 功能：读取5位按键开关状态，包含消抖功能
module Button (
    input  wire         rst,        // 复位信号
    input  wire         clk,        // 时钟信号
    input  wire [31:0]  addr,       // 地址信号
    output reg  [31:0]  rdata,      // 读数据信号

    input  wire [ 4:0]  button      // 按键开关硬件接口
);

    // TODO: 消抖相关寄存器
    reg [ 4:0] button_sync1, button_sync2;  // 同步寄存器
    reg [ 4:0] button_stable;               // 稳定状态
    reg [19:0] debounce_cnt [4:0];          // 消抖计数器 (每个按键独立)

    // TODO: 按键同步化处理
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            button_sync1 <= 5'h0;
            button_sync2 <= 5'h0;
        end else begin
            button_sync1 <= button;
            button_sync2 <= button_sync1;
        end
    end

    // TODO: 按键消抖处理 - 检测到变化后延迟15ms再确认
    genvar i;
    generate
        for (i = 0; i < 5; i = i + 1) begin: debounce_gen
            always @(posedge clk or posedge rst) begin
                if (rst) begin
                    debounce_cnt[i] <= 20'h0;
                    button_stable[i] <= 1'b0;
                end else begin
                    if (button_sync2[i] != button_stable[i]) begin
                        if (debounce_cnt[i] >= 20'd375000) begin  // 15ms @ 25MHz
                            debounce_cnt[i] <= 20'h0;
                            button_stable[i] <= button_sync2[i];
                        end else begin
                            debounce_cnt[i] <= debounce_cnt[i] + 1'b1;
                        end
                    end else begin
                        debounce_cnt[i] <= 20'h0;
                    end
                end
            end
        end
    endgenerate

    // TODO: 读按键状态
    always @(*) begin
        if (addr == `PERI_ADDR_BTN) begin
            rdata = {27'h0, button_stable};  // 高27位为0，低5位为按键状态
        end else begin
            rdata = 32'h0;
        end
    end

endmodule
