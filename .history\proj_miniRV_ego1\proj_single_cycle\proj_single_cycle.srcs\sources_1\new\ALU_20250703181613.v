`timescale 1ns / 1ps

`include "defines.vh"

// TODO: ALU模块 - 算术逻辑运算单元 - 严格按照设计规范
// 功能：完成算术、逻辑、移位和比较等运算
module ALU (
    input  wire [31:0]  A,          // 操作数A
    input  wire [31:0]  B,          // 操作数B
    input  wire [3:0]   alu_op,     // ALU操作码
    output reg  [31:0]  C,          // 运算结果
    output reg          f           // 标志位（用于分支判断）
);

    // TODO: 根据alu_op执行相应的运算 - 严格按照设计规范
    always @(*) begin
        // 默认值
        C = 32'h0;
        f = 1'b0;

        case (alu_op)
            `ALU_ADD: begin
                C = A + B;                                    // 加法
                f = 1'b0;
            end
            `ALU_SUB: begin
                C = A - B;                                    // 减法
                f = 1'b0;
            end
            `ALU_AND: begin
                C = A & B;                                    // 按位与
                f = 1'b0;
            end
            `ALU_OR: begin
                C = A | B;                                    // 按位或
                f = 1'b0;
            end
            `ALU_XOR: begin
                C = A ^ B;                                    // 按位异或
                f = 1'b0;
            end
            `ALU_SLL: begin
                C = A << B[4:0];                              // 逻辑左移
                f = 1'b0;
            end
            `ALU_SRL: begin
                C = A >> B[4:0];                              // 逻辑右移
                f = 1'b0;
            end
            `ALU_SRA: begin
                C = $signed(A) >>> B[4:0];                    // 算术右移
                f = 1'b0;
            end
            `ALU_SLT: begin
                C = ($signed(A) < $signed(B)) ? 32'h1 : 32'h0; // 有符号比较小于
                f = 1'b0;
            end
            `ALU_SLTU: begin
                C = (A < B) ? 32'h1 : 32'h0;                  // 无符号比较小于
                f = 1'b0;
            end
            `ALU_BEQ: begin
                C = 32'h0;                                    // beq不需要结果
                f = (A == B) ? 1'b1 : 1'b0;                  // 相等则f=1
            end
            `ALU_BLT: begin
                C = 32'h0;                                    // blt不需要结果
                f = ($signed(A) < $signed(B)) ? 1'b1 : 1'b0; // 小于则f=1
            end
            default: begin
                C = A + B;                                    // 默认加法
                f = 1'b0;
            end
        endcase
    end

endmodule