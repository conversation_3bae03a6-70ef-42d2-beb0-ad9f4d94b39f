// Annotate this macro before synthesis
// `define RUN_TRACE

// TODO: 在此处定义你的宏
// miniRV指令集操作码定义
`define OP_LUI      7'b0110111  // lui
`define OP_JAL      7'b1101111  // jal
`define OP_JALR     7'b1100111  // jalr
`define OP_BRANCH   7'b1100011  // beq, blt
`define OP_LOAD     7'b0000011  // lw
`define OP_STORE    7'b0100011  // sw
`define OP_IMM      7'b0010011  // addi, andi, ori, xori, slli, srli, srai
`define OP_REG      7'b0110011  // add, and, or, xor, sll, srl, sra

// TODO: ALU操作码定义 
`define ALU_ADD     4'b0000     // 加法
`define ALU_SUB     4'b0001     // 减法
`define ALU_AND     4'b0010     // 按位与
`define ALU_OR      4'b0011     // 按位或
`define ALU_XOR     4'b0100     // 按位异或
`define ALU_SLL     4'b0101     // 逻辑左移
`define ALU_SRL     4'b0110     // 逻辑右移
`define ALU_SRA     4'b0111     // 算术右移
`define ALU_SLT     4'b1000     // 有符号比较小于
`define ALU_SLTU    4'b1001     // 无符号比较小于
`define ALU_BEQ     4'b1010     // beq比较 (rs1 == rs2)
`define ALU_BLT     4'b1011     // blt比较 (rs1 < rs2)

// TODO: 立即数类型定义 
`define EXT_I       3'b000      // I型立即数
`define EXT_S       3'b001      // S型立即数
`define EXT_B       3'b010      // B型立即数
`define EXT_U       3'b011      // U型立即数
`define EXT_J       3'b100      // J型立即数
`define EXT_shift   3'b101      // 移位立即数 (slli/srli/srai)

// TODO: NPC操作类型定义 
`define NPC_PC4     2'b00       // PC + 4
`define NPC_PCIMM   2'b01       // PC + imm (分支和jal)
`define NPC_RD1IMM  2'b10       // rs1 + imm (jalr)

// TODO: 写回数据选择定义 
`define WB_ALU      2'b00       // ALU结果
`define WB_DRAM     2'b01       // 内存数据
`define WB_PC4      2'b10       // PC + 4
`define WB_EXT      2'b11       // 立即数

// TODO: ALU第二个操作数选择定义 
`define ALUB_RS2    1'b0        // 选择rs2
`define ALUB_EXT    1'b1        // 选择立即数
//

// TODO: 外设I/O接口电路的端口地址
`define PERI_ADDR_DIG   32'hFFFF_F000  // 数码管
`define PERI_ADDR_TIM0  32'hFFFF_F020  // 计时器计数值
`define PERI_ADDR_TIM1  32'hFFFF_F024  // 计时器分频系数
`define PERI_ADDR_LED   32'hFFFF_F060  // LED
`define PERI_ADDR_SW    32'hFFFF_F070  // 拨码开关
`define PERI_ADDR_BTN   32'hFFFF_F078  // 按键开关
