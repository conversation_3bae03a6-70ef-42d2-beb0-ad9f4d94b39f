<?xml version="1.0" encoding="UTF-8"?>
<GenRun Id="impl_1" LaunchPart="xc7a35tcsg324-1" LaunchTime="1751679364">
  <File Type="BITSTR-BMM" Name="miniRV_SoC_bd.bmm"/>
  <File Type="OPT-METHODOLOGY-DRC" Name="miniRV_SoC_methodology_drc_opted.rpt"/>
  <File Type="INIT-TIMING" Name="miniRV_SoC_timing_summary_init.rpt"/>
  <File Type="ROUTE-PWR" Name="miniRV_SoC_power_routed.rpt"/>
  <File Type="PA-TCL" Name="miniRV_SoC.tcl"/>
  <File Type="OPT-TIMING" Name="miniRV_SoC_timing_summary_opted.rpt"/>
  <File Type="OPT-DCP" Name="miniRV_SoC_opt.dcp"/>
  <File Type="ROUTE-PWR-SUM" Name="miniRV_SoC_power_summary_routed.pb"/>
  <File Type="REPORTS-TCL" Name="miniRV_SoC_reports.tcl"/>
  <File Type="OPT-DRC" Name="miniRV_SoC_drc_opted.rpt"/>
  <File Type="OPT-HWDEF" Name="miniRV_SoC.hwdef"/>
  <File Type="PWROPT-DCP" Name="miniRV_SoC_pwropt.dcp"/>
  <File Type="PWROPT-DRC" Name="miniRV_SoC_drc_pwropted.rpt"/>
  <File Type="PWROPT-TIMING" Name="miniRV_SoC_timing_summary_pwropted.rpt"/>
  <File Type="PLACE-DCP" Name="miniRV_SoC_placed.dcp"/>
  <File Type="PLACE-IO" Name="miniRV_SoC_io_placed.rpt"/>
  <File Type="PLACE-CLK" Name="miniRV_SoC_clock_utilization_placed.rpt"/>
  <File Type="PLACE-UTIL" Name="miniRV_SoC_utilization_placed.rpt"/>
  <File Type="PLACE-UTIL-PB" Name="miniRV_SoC_utilization_placed.pb"/>
  <File Type="PLACE-CTRL" Name="miniRV_SoC_control_sets_placed.rpt"/>
  <File Type="PLACE-SIMILARITY" Name="miniRV_SoC_incremental_reuse_placed.rpt"/>
  <File Type="PLACE-PRE-SIMILARITY" Name="miniRV_SoC_incremental_reuse_pre_placed.rpt"/>
  <File Type="BG-BGN" Name="miniRV_SoC.bgn"/>
  <File Type="PLACE-TIMING" Name="miniRV_SoC_timing_summary_placed.rpt"/>
  <File Type="POSTPLACE-PWROPT-DCP" Name="miniRV_SoC_postplace_pwropt.dcp"/>
  <File Type="BG-BIN" Name="miniRV_SoC.bin"/>
  <File Type="POSTPLACE-PWROPT-TIMING" Name="miniRV_SoC_timing_summary_postplace_pwropted.rpt"/>
  <File Type="PHYSOPT-DCP" Name="miniRV_SoC_physopt.dcp"/>
  <File Type="PHYSOPT-DRC" Name="miniRV_SoC_drc_physopted.rpt"/>
  <File Type="BITSTR-MSK" Name="miniRV_SoC.msk"/>
  <File Type="PHYSOPT-TIMING" Name="miniRV_SoC_timing_summary_physopted.rpt"/>
  <File Type="ROUTE-ERROR-DCP" Name="miniRV_SoC_routed_error.dcp"/>
  <File Type="ROUTE-DCP" Name="miniRV_SoC_routed.dcp"/>
  <File Type="ROUTE-BLACKBOX-DCP" Name="miniRV_SoC_routed_bb.dcp"/>
  <File Type="ROUTE-DRC" Name="miniRV_SoC_drc_routed.rpt"/>
  <File Type="ROUTE-DRC-PB" Name="miniRV_SoC_drc_routed.pb"/>
  <File Type="BITSTR-LTX" Name="debug_nets.ltx"/>
  <File Type="BITSTR-LTX" Name="miniRV_SoC.ltx"/>
  <File Type="ROUTE-DRC-RPX" Name="miniRV_SoC_drc_routed.rpx"/>
  <File Type="BITSTR-MMI" Name="miniRV_SoC.mmi"/>
  <File Type="ROUTE-METHODOLOGY-DRC" Name="miniRV_SoC_methodology_drc_routed.rpt"/>
  <File Type="ROUTE-METHODOLOGY-DRC-RPX" Name="miniRV_SoC_methodology_drc_routed.rpx"/>
  <File Type="BITSTR-SYSDEF" Name="miniRV_SoC.sysdef"/>
  <File Type="ROUTE-METHODOLOGY-DRC-PB" Name="miniRV_SoC_methodology_drc_routed.pb"/>
  <File Type="ROUTE-PWR-RPX" Name="miniRV_SoC_power_routed.rpx"/>
  <File Type="ROUTE-STATUS" Name="miniRV_SoC_route_status.rpt"/>
  <File Type="ROUTE-STATUS-PB" Name="miniRV_SoC_route_status.pb"/>
  <File Type="ROUTE-TIMINGSUMMARY" Name="miniRV_SoC_timing_summary_routed.rpt"/>
  <File Type="ROUTE-TIMING-PB" Name="miniRV_SoC_timing_summary_routed.pb"/>
  <File Type="ROUTE-TIMING-RPX" Name="miniRV_SoC_timing_summary_routed.rpx"/>
  <File Type="ROUTE-SIMILARITY" Name="miniRV_SoC_incremental_reuse_routed.rpt"/>
  <File Type="ROUTE-CLK" Name="miniRV_SoC_clock_utilization_routed.rpt"/>
  <File Type="ROUTE-BUS-SKEW" Name="miniRV_SoC_bus_skew_routed.rpt"/>
  <File Type="ROUTE-BUS-SKEW-PB" Name="miniRV_SoC_bus_skew_routed.pb"/>
  <File Type="ROUTE-BUS-SKEW-RPX" Name="miniRV_SoC_bus_skew_routed.rpx"/>
  <File Type="POSTROUTE-PHYSOPT-DCP" Name="miniRV_SoC_postroute_physopt.dcp"/>
  <File Type="POSTROUTE-PHYSOPT-BLACKBOX-DCP" Name="miniRV_SoC_postroute_physopt_bb.dcp"/>
  <File Type="POSTROUTE-PHYSOPT-TIMING" Name="miniRV_SoC_timing_summary_postroute_physopted.rpt"/>
  <File Type="POSTROUTE-PHYSOPT-TIMING-PB" Name="miniRV_SoC_timing_summary_postroute_physopted.pb"/>
  <File Type="POSTROUTE-PHYSOPT-TIMING-RPX" Name="miniRV_SoC_timing_summary_postroute_physopted.rpx"/>
  <File Type="POSTROUTE-PHYSOPT-BUS-SKEW" Name="miniRV_SoC_bus_skew_postroute_physopted.rpt"/>
  <File Type="POSTROUTE-PHYSOPT-BUS-SKEW-PB" Name="miniRV_SoC_bus_skew_postroute_physopted.pb"/>
  <File Type="BG-BIT" Name="miniRV_SoC.bit"/>
  <File Type="POSTROUTE-PHYSOPT-BUS-SKEW-RPX" Name="miniRV_SoC_bus_skew_postroute_physopted.rpx"/>
  <File Type="BITSTR-RBT" Name="miniRV_SoC.rbt"/>
  <File Type="BITSTR-NKY" Name="miniRV_SoC.nky"/>
  <File Type="BG-DRC" Name="miniRV_SoC.drc"/>
  <File Type="RDI-RDI" Name="miniRV_SoC.vdi"/>
  <File Type="WBT-USG" Name="usage_statistics_webtalk.html"/>
  <FileSet Name="sources" Type="DesignSrcs" RelSrcDir="$PSRCDIR/sources_1">
    <Filter Type="Srcs"/>
    <File Path="$PSRCDIR/sources_1/new/defines.vh">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/ALU.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Bridge.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Button.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Ctrl.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Dig.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Led.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/NPC.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/PC.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/RF.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/SEXT.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Switch.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/Timer.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/myCPU.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/new/miniRV_SoC.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/test.coe">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="c:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/test.coe">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="DesignMode" Val="RTL"/>
      <Option Name="TopModule" Val="miniRV_SoC"/>
      <Option Name="TopAutoSet" Val="TRUE"/>
    </Config>
  </FileSet>
  <FileSet Name="constrs_in" Type="Constrs" RelSrcDir="$PSRCDIR/constrs_1">
    <Filter Type="Constrs"/>
    <File Path="$PSRCDIR/constrs_1/new/miniRV_clock.xdc">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/constrs_1/new/miniRV_SoC.xdc">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="TargetConstrsFile" Val="$PSRCDIR/constrs_1/new/miniRV_SoC.xdc"/>
      <Option Name="ConstrsType" Val="XDC"/>
    </Config>
  </FileSet>
  <FileSet Name="utils" Type="Utils" RelSrcDir="$PSRCDIR/utils_1">
    <Filter Type="Utils"/>
    <Config>
      <Option Name="TopAutoSet" Val="TRUE"/>
    </Config>
  </FileSet>
  <Strategy Version="1" Minor="2">
    <StratHandle Name="Vivado Implementation Defaults" Flow="Vivado Implementation 2018">
      <Desc>Default settings for Implementation.</Desc>
    </StratHandle>
    <Step Id="init_design"/>
    <Step Id="opt_design"/>
    <Step Id="power_opt_design"/>
    <Step Id="place_design"/>
    <Step Id="post_place_power_opt_design"/>
    <Step Id="phys_opt_design"/>
    <Step Id="route_design"/>
    <Step Id="post_route_phys_opt_design"/>
    <Step Id="write_bitstream"/>
  </Strategy>
  <BlockFileSet Type="BlockSrcs" Name="DRAM"/>
  <BlockFileSet Type="BlockSrcs" Name="cpuclk"/>
  <BlockFileSet Type="BlockSrcs" Name="IROM"/>
</GenRun>
